spring:
  datasource:
    druid:
#      #Oracle
      driver-class-name: oracle.jdbc.OracleDriver
      url: ********************************************
      username: gk_report_zjj
      password: gk_report_zjj
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 6000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      #Oracle需要打开注释
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        #login-username: admin
        #login-password: admin
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true
  mail:
    host: smtp.qq.com
    port: 465
    username: <EMAIL>
    password: fhtzkvvwjdbsbdic
    #    properties:
    #      mail:
    #        smtp:
    #          socketFactory:
    #            port: 465
    #            class: javax.net.ssl.SSLSocketFactory
    #          ssl:
    #            enable: true
    properties.mail.smtp.starttls.enable: true
    properties.mail.smtp.starttls.required: true
    properties.mail.smtp.ssl.enable: true
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 1000MB
      enabled: true
  redis:
    database: 7
    host: localhost
    port: 6379
    password:    # 密码xygk@123（默认为空）
    timeout: 6000ms  # 连接超时时长（毫秒）
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接

  rabbitmq:
    #    host: ***************
    host: localhost
    port: 5672
    username: xygk
    password: xygk@123
    virtual-host: /

renren.redis.open: true
#系统相关配置
system:
  configure:
    #用户信息
    SystemUserInformation:
      secretkey: 92E2F30599B7F780E6BBEE77A16BB105
      #      农商银行："username":"lznxyh","password":"bYxoP74O",secretkey:92E2F30599B7F780E6BBEE77A16BB105
    smzApi:
      apiRoot: http://**************:7009/xygk/
#      apiRoot: http://localhost:7009/xygk/
