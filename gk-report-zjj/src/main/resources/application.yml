#此处移除tomact,使用undertow作为内嵌容器
# undertow
server:
  undertow:
    # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
    # 不要设置过大，如果过大，启动项目会报错：打开文件数过多
    io-threads: 4
    # 阻塞任务线程池, 当执行类似servlet请求阻塞IO操作, undertow会从这个线程池中取得线程
    # 它的值设置取决于系统线程执行任务的阻塞系数，默认值是IO线程数*8
    worker-threads: 20
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    # 每块buffer的空间大小,越小的空间被利用越充分，不要设置太大，以免影响其他应用，合适即可
    buffer-size: 1024
    # 是否分配的直接内存
    direct-buffers: true
  #    uri-encoding: UTF-8
  #    max-threads: 1000
  #    min-spare-threads: 30
  port: 10014
  servlet:
    context-path: /xygk
    session:
      cookie:
        http-only: true
    encoding:
      force-response: true
  tomcat:
    connection-timeout: 5000ms

# mysql
spring:
  # 环境 dev|test|prod
  profiles:
    active: dev
  messages:
    encoding: UTF-8
    basename: i18n/messages
  # jackson时间格式化
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
#  redis:
#    database: 0
#    host: localhost
#    port: 6379
#    password:    # 密码xygk@123（默认为空）
#    timeout: 6000ms  # 连接超时时长（毫秒）
#    jedis:
#      pool:
#        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
#        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
#        max-idle: 10      # 连接池中的最大空闲连接
#        min-idle: 5       # 连接池中的最小空闲连接

renren:
  redis:
    open: false  # 是否开启redis缓存  true开启   false关闭

#mybatis
mybatis-plus:
  mapper-locations: classpath:/mapper/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: io.renren.entity;io.renren.modules.*.entity
  global-config:
    #数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: INPUT
      #字段策略 IGNORED:"忽略判断",NOT_NULL:"非 NULL 判断"),NOT_EMPTY:"非空判断"
      field-strategy: NOT_NULL
      #驼峰下划线转换
      column-underline: true
      #db-type: oracle
    banner: false
  #原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'

#文件上传配置
file:
  #mac系统
  macPath: ~/upload/
  #windows服务器上传的路径
  windowsPath: D:/upload
  #linux服务器上传的路径
  linuxPath: /home/<USER>/

#附件服务器地址
fdfs:
  so-timeout: 600000
  connect-timeout: 6000
  thumb-image:
    height: 150
    width: 150
  tracker-list:
    - 182.151.58.153:22122
  web-server-url: http://182.151.58.153:8089/