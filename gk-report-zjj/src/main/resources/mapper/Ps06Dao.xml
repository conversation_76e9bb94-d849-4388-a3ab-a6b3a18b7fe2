<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.dao.Ps06Dao">

    <resultMap type="io.renren.entity.Ps06Entity" id="ps06Map">
        <result property="ps0601" column="PS0601"/>
        <result property="ps0201" column="PS0201"/>
        <result property="entryOrExitTime" column="ENTRY_OR_EXIT_TIME"/>
        <result property="inOrOut" column="IN_OR_OUT"/>
        <result property="memo" column="MEMO"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="stupstatus" column="STUPSTATUS"/>
        <result property="upmsg" column="UPMSG"/>
    </resultMap>
    <select id="getNeedWokerExit" resultType="io.renren.dto.Ps06DTO">
        SELECT * FROM
        (SELECT a.ps0601,a.entry_or_exit_time exittime,d.code projectcode,c.idcardnumber,rownum rn,d.bank_code bankcode
        FROM b_ps06 a,b_ps02 b,b_ps01 c,b_pj01 d
        where a.ps0201=b.ps0201 and b.ps0101=c.ps0101 and b.pj0101=d.pj0101
        and a.stupstatus='0' and b.stupstatus='1' and d.whether_report='1'
        and a.in_or_out='2') e where e.rn &lt;= 1000
    </select>


</mapper>