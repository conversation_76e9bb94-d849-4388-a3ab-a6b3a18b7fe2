<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.dao.Tm01Dao">

    <resultMap type="io.renren.entity.Tm01Entity" id="tm01Map">
        <result property="tm0101" column="TM0101"/>
        <result property="cp0201" column="CP0201"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="teamsysno" column="TEAMSYSNO"/>
        <result property="teamname" column="TEAMNAME"/>
        <result property="responsiblepersonname" column="RESPONSIBLEPERSONNAME"/>
        <result property="responsiblepersonphone" column="RESPONSIBLEPERSONPHONE"/>
        <result property="idcardtype" column="IDCARDTYPE"/>
        <result property="responsiblepersonidnumber" column="RESPONSIBLEPERSONIDNUMBER"/>
        <result property="entrytime" column="ENTRYTIME"/>
        <result property="exittime" column="EXITTIME"/>
        <result property="inOrOut" column="IN_OR_OUT"/>
        <result property="memo" column="MEMO"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="stupstatus" column="STUPSTATUS"/>
    </resultMap>
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            UPDATE b_tm01
            SET
            stupstatus = #{item.stupstatus},
            upmsg = #{item.upmsg},
            teamsysno = #{item.teamsysno},
            update_date = sysdate
            WHERE teamname = #{item.teamname} and pj0101 = #{item.pj0101}
        </foreach>
    </update>
    <select id="getNeedUpData" resultType="io.renren.dto.Tm01DTO">
        SELECT * FROM
        (SELECT a.tm0101,b.code projectcode,d.corpcode corpcode,a.teamname teamname,a.pj0101,
        a.responsiblepersonname,a.responsiblepersonphone,a.idcardtype,a.responsiblepersonidnumber,
        a.entrytime,a.teamsysno,b.bank_code bankcode,rownum rn
        FROM b_tm01 a,b_pj01 b,b_cp02 c,b_cp01 d
        where a.pj0101=b.pj0101 and a.cp0201=c.cp0201
        and c.cp0101=d.cp0101 and a.stupstatus='0'
        and b.stupstatus='1' and b.whether_report='1'
        and c.stupstatus='1' and c.auditstatus='1') e where e.rn &lt;= 100
    </select>


</mapper>