<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.dao.Ps04Dao">

    <resultMap type="io.renren.entity.Ps04Entity" id="ps04Map">
        <result property="ps0401" column="PS0401"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="cp0201" column="CP0201"/>
        <result property="ps0301" column="PS0301"/>
        <result property="jobtype" column="JOBTYPE"/>
        <result property="photo" column="PHOTO"/>
        <result property="entrytime" column="ENTRYTIME"/>
        <result property="exittime" column="EXITTIME"/>
        <result property="inOrOut" column="IN_OR_OUT"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="stupstatus" column="STUPSTATUS"/>
        <result property="isconfirm" column="ISCONFIRM"/>
        <result property="confirmresult" column="CONFIRMRESULT"/>
        <result property="confirmor" column="CONFIRMOR"/>
        <result property="confirmDate" column="CONFIRM_DATE"/>
        <result property="upmsg" column="UPMSG"/>
        <result property="auditstatus" column="AUDITSTATUS"/>
        <result property="auditmsg" column="AUDITMSG"/>
    </resultMap>
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            UPDATE b_ps04
            SET
            stupstatus = #{item.stupstatus},
            upmsg = #{item.upmsg},
            update_date = sysdate
            WHERE pj0101 = #{item.pj0101} and ps0301 = (select ps0301 from b_ps03 where cp0101=(select cp0101 from b_cp01 where corpcode=#{corpcode})
            and ps0101=(select ps0101 from b_ps01 where idcardnumber=#{item.idcardnumber}))
        </foreach>
    </update>
    <select id="getNeedUpData" resultType="io.renren.dto.Ps04DTO">
        SELECT f.*,(select b.corpcode from b_cp01 b where f.cp0101=b.cp0101)corpcode FROM
        (select a.ps0401,a.pj0101,b.cp0101,d.code projectcode,c.idcardnumber idcardnumber,a.jobtype,
        a.entrytime,'0' hascontract,b.hasbuyinsurance hasbuyinsurance,d.bank_code bankcode,rownum rn
        from b_ps04 a,b_ps03 b,b_ps01 c,b_pj01 d,b_cp02 e
        where a.ps0301=b.ps0301 and b.ps0101=c.ps0101 and a.pj0101=d.pj0101
        and a.cp0201=e.cp0201
        and a.stupstatus='0' and c.stupstatus='1'
        and d.stupstatus='1' and d.whether_report='1'
        and e.stupstatus='1' and e.auditstatus='1'
        and a.exittime is null) f where f.rn &lt;= 50
    </select>
    <select id="getNeedStatus" resultType="io.renren.dto.Ps04DTO">
        SELECT a.ps0401,c.idcardnumber,d.code projectcode,d.bank_code bankcode,a.pj0101
        FROM b_ps04 a,b_ps03 b,b_ps01 c,b_pj01 d
        where a.ps0301=b.ps0301 and b.ps0101=c.ps0101
          and a.pj0101=d.pj0101 and a.stupstatus='1'
          and a.auditstatus='0'
    </select>


</mapper>