<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.dao.Ps07Dao">

    <resultMap type="io.renren.entity.Ps07Entity" id="ps07Map">
        <result property="ps0701" column="PS0701"/>
        <result property="ps0401" column="PS0401"/>
        <result property="entryOrExitTime" column="ENTRY_OR_EXIT_TIME"/>
        <result property="inOrOut" column="IN_OR_OUT"/>
        <result property="memo" column="MEMO"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="stupstatus" column="STUPSTATUS"/>
        <result property="upmsg" column="UPMSG"/>
    </resultMap>
    <select id="getNeedManagerExit" resultType="io.renren.dto.Ps07DTO">
        SELECT * FROM
        (SELECT a.ps0701,e.bank_code bankcode,e.code projectcode,d.idcardnumber,a.entry_or_exit_time exittime,rownum rn
        FROM b_ps07 a, b_ps04 b, b_ps03 c, b_ps01 d, b_pj01 e
        where a.ps0401 = b.ps0401
        and b.ps0301 = c.ps0301
        and c.ps0101 = d.ps0101
        and b.pj0101 = e.pj0101
        and a.stupstatus = '0'
        and b.stupstatus = '1'
        and e.whether_report = '1'
        and a.in_or_out = '2') g where g.rn &lt;= 1000
    </select>


</mapper>