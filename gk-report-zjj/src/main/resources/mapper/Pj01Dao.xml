<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.dao.Pj01Dao">

    <resultMap type="io.renren.entity.Pj01Entity" id="pj01Map">
        <result property="pj0101" column="PJ0101"/>
        <result property="safetyno" column="SAFETYNO"/>
        <result property="code" column="CODE"/>
        <result property="name" column="NAME"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="industry" column="INDUSTRY"/>
        <result property="category" column="CATEGORY"/>
        <result property="constructtype" column="CONSTRUCTTYPE"/>
        <result property="investtype" column="INVESTTYPE"/>
        <result property="areacode" column="AREACODE"/>
        <result property="address" column="ADDRESS"/>
        <result property="buildingarea" column="BUILDINGAREA"/>
        <result property="buildinglength" column="BUILDINGLENGTH"/>
        <result property="invest" column="INVEST"/>
        <result property="engineering" column="ENGINEERING"/>
        <result property="scale" column="SCALE"/>
        <result property="startdate" column="STARTDATE"/>
        <result property="completeDate" column="COMPLETE_DATE"/>
        <result property="lng" column="LNG"/>
        <result property="lat" column="LAT"/>
        <result property="linkman" column="LINKMAN"/>
        <result property="linkphone" column="LINKPHONE"/>
        <result property="ismajorProject" column="ISMAJOR_PROJECT"/>
        <result property="isdeposit" column="ISDEPOSIT"/>
        <result property="prjstatus" column="PRJSTATUS"/>
        <result property="deptId" column="DEPT_ID"/>
        <result property="stupstatus" column="STUPSTATUS"/>
        <result property="markLocation" column="MARK_LOCATION"/>
        <result property="ps1201" column="PS1201"/>
        <result property="region" column="REGION"/>
        <result property="whetherReport" column="WHETHER_REPORT"/>
        <result property="bankCode" column="BANK_CODE"/>
    </resultMap>
    <select id="getNeedUpData" resultType="io.renren.dto.Pj01DTO">
        SELECT * FROM
        (SELECT a.pj0101,a.name,a.category,a.areacode,a.invest,a.buildingarea,a.startdate,a.complete_date completedate,a.prjstatus,
        a.lng,a.lat,a.address,a.scale prjsize,a.constructtype constructtype,a.investtype,a.areacode virareacode,a.bank_code bankcode,c.corpcode,b.entrytime,d.special_account_no banknumber,
        d.special_account banknumbername,d.BANK_NAME bankname,d.pay_bank_code payrolltopbankcode,rownum rn
        FROM b_pj01 a,b_cp02 b,b_cp01 c,b_pa01 d where a.pj0101=b.pj0101 and b.cp0101=c.cp0101 and b.corptype='9'
        and a.whether_report='1' and a.stupstatus='0' and c.stupstatus='1' and a.pj0101=d.pj0101 and d.special_account_no is not null) e
        where e.rn &lt;= 1
    </select>


</mapper>