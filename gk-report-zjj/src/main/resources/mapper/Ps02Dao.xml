<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.dao.Ps02Dao">

    <resultMap type="io.renren.entity.Ps02Entity" id="ps02Map">
        <result property="ps0201" column="PS0201"/>
        <result property="ps0101" column="PS0101"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="tm0101" column="TM0101"/>
        <result property="isteamleader" column="ISTEAMLEADER"/>
        <result property="worktypecode" column="WORKTYPECODE"/>
        <result property="payrollbankcardnumber" column="PAYROLLBANKCARDNUMBER"/>
        <result property="payrollbankname" column="PAYROLLBANKNAME"/>
        <result property="payrolltopbankcode" column="PAYROLLTOPBANKCODE"/>
        <result property="issuecardpicurl" column="ISSUECARDPICURL"/>
        <result property="hasbuyinsurance" column="HASBUYINSURANCE"/>
        <result property="entrytime" column="ENTRYTIME"/>
        <result property="exittime" column="EXITTIME"/>
        <result property="inOrOut" column="IN_OR_OUT"/>
        <result property="memo" column="MEMO"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="payrollno" column="PAYROLLNO"/>
        <result property="stupstatus" column="STUPSTATUS"/>
        <result property="upmsg" column="UPMSG"/>
    </resultMap>
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            UPDATE b_ps02
            SET
            stupstatus = #{item.stupstatus},
            upmsg = #{item.upmsg},
            update_date = sysdate
            WHERE pj0101 = #{item.pj0101} and ps0101 = (select ps0101 from b_ps01 where idcardnumber=#{item.idcardnumber})
        </foreach>
    </update>
    <select id="getNeedUpData" resultType="io.renren.dto.Ps02DTO">
        SELECT f.*,(select b.corpcode from b_cp02 a,b_cp01 b where a.cp0101=b.cp0101 and a.cp0201=f.cp0201)corpcode FROM
        (select a.ps0201,a.pj0101,d.cp0201,d.teamsysno teamsysno,b.idcardnumber idcardnumber,
        a.isteamleader isteamleader,a.worktypecode worktypecode,a.entrytime entrytime,a.issuecardpicurl issuecardpicurl,
        '0' hascontract,a.hasbuyinsurance hasbuyinsurance,a.payrollbankcardnumber payrollbankcardnumber,
        a.payrollbankname payrollbankname,a.payrolltopbankcode payrolltopbankcode,e.special_account_no paybankcardnumber,
        e.bank_name paybankname,e.pay_bank_code paybankcode,c.code projectcode,c.bank_code bankcode,rownum rn
        from b_ps02 a,b_ps01 b,b_pj01 c,b_tm01 d,b_pa01 e
        where a.ps0101=b.ps0101 and a.pj0101=c.pj0101 and a.tm0101=d.tm0101
        and a.pj0101=d.pj0101 and a.stupstatus='0' and b.stupstatus='1'
        and c.stupstatus='1' and c.whether_report='1' and d.stupstatus='1'
        and e.special_account_no is not null) f where f.rn &lt;= 100
    </select>


</mapper>