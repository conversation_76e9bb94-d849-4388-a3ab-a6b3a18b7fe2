<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.dao.Cp02Dao">

    <resultMap type="io.renren.entity.Cp02Entity" id="cp02Map">
        <result property="cp0201" column="CP0201"/>
        <result property="cp0101" column="CP0101"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="corptype" column="CORPTYPE"/>
        <result property="entrytime" column="ENTRYTIME"/>
        <result property="exittime" column="EXITTIME"/>
        <result property="inOrOut" column="IN_OR_OUT"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="stupstatus" column="STUPSTATUS"/>
    </resultMap>
    <select id="getNeedUpData" resultType="io.renren.dto.Cp02DTO">
        SELECT d.*,e.special_account_no banknumber,e.special_account banknumbername,e.bank_name bankname,
        e.pay_bank_code payrolltopbankcode FROM
        (SELECT a.cp0201,b.code projectcode,c.corpcode corpcode,a.corptype corpintype,
        a.entrytime entrytime,b.bank_code bankcode,a.pj0101,rownum rn
        FROM b_cp02 a,b_pj01 b,b_cp01 c
        where a.pj0101=b.pj0101 and a.cp0101=c.cp0101
        and b.stupstatus='1' and a.stupstatus='0'
        and c.stupstatus='1' and b.whether_report='1') d
        left join b_pa01 e on d.pj0101=e.pj0101
        and d.rn &lt;= 1
    </select>
    <select id="getNeedStatus" resultType="io.renren.dto.Cp02DTO">
        SELECT a.cp0201,b.corpcode,c.code projectcode,c.bank_code bankcode
        FROM b_cp02 a,b_cp01 b,b_pj01 c
        where a.cp0101=b.cp0101 and a.pj0101=c.pj0101
          and a.stupstatus='1' and a.auditstatus='0'
    </select>


</mapper>