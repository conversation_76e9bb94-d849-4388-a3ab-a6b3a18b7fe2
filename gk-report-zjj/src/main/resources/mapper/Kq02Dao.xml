<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.dao.Kq02Dao">

    <resultMap type="io.renren.entity.Kq02Entity" id="kq02Map">
        <result property="kq0201" column="KQ0201"/>
        <result property="userId" column="USER_ID"/>
        <result property="personName" column="PERSON_NAME"/>
        <result property="personType" column="PERSON_TYPE"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="deviceserialno" column="DEVICESERIALNO"/>
        <result property="checkdate" column="CHECKDATE"/>
        <result property="direction" column="DIRECTION"/>
        <result property="attendtype" column="ATTENDTYPE"/>
        <result property="lng" column="LNG"/>
        <result property="lat" column="LAT"/>
        <result property="imageUrl" column="IMAGE_URL"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="ismakecard" column="ISMAKECARD"/>
        <result property="tm0101" column="TM0101"/>
        <result property="stupstatus" column="STUPSTATUS"/>
    </resultMap>
    <update id="batchUpdate" parameterType="java.util.List">
        update b_kq02 set stupstatus='1' where kq0201 in
        <foreach collection="list" index="index" open="(" close=")" item="item" separator=",">
            #{item.kq0201}
        </foreach>
    </update>
    <select id="getNeedUpWokerData" resultType="io.renren.dto.Kq02DTO">
        SELECT * FROM
        (select a.kq0201,a.pj0101,a.person_type,d.code projectcode,rownum rn,
        (select b.corpcode from b_cp02 a,b_cp01 b where a.cp0101=b.cp0101 and a.cp0201=e.cp0201) corpcode,
        e.teamsysno,c.idcardnumber idcardnumber,a.checkdate checkdate,a.direction direction,d.bank_code bankcode
        from b_kq02 a,b_ps02 b,b_ps01 c,b_pj01 d,b_tm01 e
        where a.user_id=b.ps0201 and b.ps0101=c.ps0101 and a.pj0101=d.pj0101
        and a.tm0101=e.tm0101 and a.stupstatus='0' and b.stupstatus='1'
        and d.stupstatus='1' and d.whether_report='1' and e.stupstatus='1'
        and e.stupstatus='1' and b.exittime is null and a.person_type='1'
        order by a.checkdate desc) g where g.rn &lt;= 1000
    </select>
    <select id="getNeedUpManagerData" resultType="io.renren.dto.Kq02DTO">
        SELECT * FROM
        (select a.kq0201,a.pj0101,e.code projectcode,d.idcardnumber idcardnumber,
        a.checkdate checkdate,a.direction direction,rownum rn
        from b_kq02 a,b_ps04 b,b_ps03 c,b_ps01 d,b_pj01 e where a.user_id=b.ps0401
        and b.ps0301=c.ps0301 and c.ps0101=d.ps0101 and a.pj0101=e.pj0101
        and b.stupstatus='1' and b.auditstatus='1' and e.stupstatus='1'
        and e.whether_report='1' and a.person_type='2' and b.exittime is null
        order by a.checkdate desc) g where g.rn &lt;= 1000
    </select>


</mapper>