<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.dao.ZDicConversionDao">

    <resultMap type="io.renren.entity.ZDicConversionEntity" id="zDicConversionMap">
        <result property="type" column="TYPE"/>
        <result property="actualValue" column="ACTUAL_VALUE"/>
        <result property="conversionValue" column="CONVERSION_VALUE"/>
    </resultMap>
    <select id="selectConversionType" resultType="string">
        select a.type from Z_DIC_CONVERSION a group by a.type
    </select>

    <select id="selectByTypeToMap" resultType="java.util.Map" >
        select a.ACTUAL_VALUE actualValue,a.CONVERSION_VALUE conversionValue from Z_DIC_CONVERSION a where a.type=#{type}
    </select>
</mapper>