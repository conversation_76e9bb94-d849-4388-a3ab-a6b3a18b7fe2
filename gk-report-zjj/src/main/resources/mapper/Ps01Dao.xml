<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.dao.Ps01Dao">

    <resultMap type="io.renren.entity.Ps01Entity" id="ps01Map">
        <result property="ps0101" column="PS0101"/>
        <result property="name" column="NAME"/>
        <result property="idcardtype" column="IDCARDTYPE"/>
        <result property="idcardnumber" column="IDCARDNUMBER"/>
        <result property="gender" column="GENDER"/>
        <result property="nation" column="NATION"/>
        <result property="birthday" column="BIRTHDAY"/>
        <result property="address" column="ADDRESS"/>
        <result property="edulevel" column="EDULEVEL"/>
        <result property="degree" column="DEGREE"/>
        <result property="workertype" column="WORKERTYPE"/>
        <result property="areacode" column="AREACODE"/>
        <result property="headimageurl" column="HEADIMAGEURL"/>
        <result property="politicstype" column="POLITICSTYPE"/>
        <result property="isjoined" column="ISJOINED"/>
        <result property="joinedtime" column="JOINEDTIME"/>
        <result property="cellphone" column="CELLPHONE"/>
        <result property="cultureleveltype" column="CULTURELEVELTYPE"/>
        <result property="specialty" column="SPECIALTY"/>
        <result property="hasbadmedicalhistory" column="HASBADMEDICALHISTORY"/>
        <result property="urgentlinkman" column="URGENTLINKMAN"/>
        <result property="urgentlinkmanphone" column="URGENTLINKMANPHONE"/>
        <result property="workdate" column="WORKDATE"/>
        <result property="maritalstatus" column="MARITALSTATUS"/>
        <result property="grantorg" column="GRANTORG"/>
        <result property="positiveidcardimageurl" column="POSITIVEIDCARDIMAGEURL"/>
        <result property="negativeidcardimageurl" column="NEGATIVEIDCARDIMAGEURL"/>
        <result property="startdate" column="STARTDATE"/>
        <result property="expirydate" column="EXPIRYDATE"/>
        <result property="stupstatus" column="STUPSTATUS"/>
    </resultMap>
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            UPDATE b_ps01
            SET
            stupstatus = #{item.stupstatus},
            upmsg = #{item.upmsg},
            update_date = sysdate
            WHERE name = #{item.name} and idcardnumber = #{item.idcardnumber}
        </foreach>
    </update>
    <select id="getNeedUpData" resultType="io.renren.dto.Ps01DTO">
        SELECT ps0101,
               name,
               idcardtype,
               idcardnumber,
               gender,
               nation,
               workertype,
               areacode,
               address,
               politicstype,
               cellphone,
               cultureleveltype,
               grantorg,
               '313' bank_code
        FROM (SELECT a.*, rownum rn FROM b_ps01 a where a.stupstatus = '0' order by a.ps0101 desc) b
        where b.rn &lt;= 500
    </select>


</mapper>