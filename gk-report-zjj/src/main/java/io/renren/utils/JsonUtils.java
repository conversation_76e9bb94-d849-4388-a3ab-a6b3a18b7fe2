package io.renren.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

public class JsonUtils {

    /**
     * 校验是否为合法的json字符串
     * @param str
     * @return
     */
    public static boolean isJSONValid(String str) {
        try {
            JSONObject object = JSON.parseObject(str);
        } catch (Exception e) {
            try {
                JSONArray array = JSON.parseArray(str);
            } catch (Exception ex) {
                ex.printStackTrace();
                return false;
            }
        }
        return true;
    }
}
