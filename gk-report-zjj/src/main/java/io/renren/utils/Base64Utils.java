package io.renren.utils;


import cn.hutool.http.HttpUtil;
import io.renren.common.file.ImageUtil;
import io.renren.config.FileProperties;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;

@Component
public class Base64Utils {
    /** */
    /**
     * 文件读取缓冲区大小
     */
    private static final int CACHE_SIZE = 1024;
    private static final Log logger = LogFactory.getLog(Base64Utils.class);
    private static final double accuracy = 0.95d;
    private static String pic = "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";

    private static final String DEFAULT_IMAGE_URL = "https://jzgappendix.xygkcloud.com/gk-cloud/test.jpg";
    private static FileProperties fileProperties;
    private static final long IMAGE_SIZE = 47 * 1024;

    @Autowired
    public void init(FileProperties fileProperties) {
        Base64Utils.fileProperties = fileProperties;
    }
    /** */
    /**
     * <p>
     * BASE64字符串解码为二进制数据
     * </p>
     *
     * @param base64
     * @return
     * @throws Exception
     */
    public static byte[] decode(String base64) throws Exception {
        return Base64.decodeBase64(base64.getBytes());
    }

    /** */
    /**
     * <p>
     * 二进制数据编码为BASE64字符串
     * </p>
     *
     * @param bytes
     * @return
     * @throws Exception
     */
    public static String encode(byte[] bytes) throws Exception {
        return new String(Base64.encodeBase64(bytes));
    }

    /** */
    /**
     * <p>
     * 将文件编码为BASE64字符串
     * </p>
     * <p>
     * 大文件慎用，可能会导致内存溢出
     * </p>
     *
     * @param filePath 文件绝对路径
     * @return
     * @throws Exception
     */
    public static String encodeFile(String filePath) throws Exception {
        byte[] bytes = fileToByte(filePath);
        return encode(bytes);
    }

    /** */
    /**
     * <p>
     * BASE64字符串转回文件
     * </p>
     *
     * @param filePath 文件绝对路径
     * @param base64   编码字符串
     * @throws Exception
     */
    public static void decodeToFile(String filePath, String base64) throws Exception {
        byte[] bytes = decode(base64);
        byteArrayToFile(bytes, filePath);
    }

    /** */
    /**
     * <p>
     * 文件转换为二进制数组
     * </p>
     *
     * @param filePath 文件路径
     * @return
     * @throws Exception
     */
    public static byte[] fileToByte(String filePath) throws Exception {
        byte[] data = new byte[0];
        File file = new File(filePath);
        if (file.exists()) {
            FileInputStream in = new FileInputStream(file);
            ByteArrayOutputStream out = new ByteArrayOutputStream(2048);
            byte[] cache = new byte[CACHE_SIZE];
            int nRead = 0;
            while ((nRead = in.read(cache)) != -1) {
                out.write(cache, 0, nRead);
                out.flush();
            }
            out.close();
            in.close();
            data = out.toByteArray();
        }
        return data;
    }

    /** */
    /**
     * <p>
     * 二进制数据写文件
     * </p>
     *
     * @param bytes    二进制数据
     * @param filePath 文件生成目录
     */
    public static void byteArrayToFile(byte[] bytes, String filePath) throws Exception {
        InputStream in = new ByteArrayInputStream(bytes);
        File destFile = new File(filePath);
        if (!destFile.getParentFile().exists()) {
            destFile.getParentFile().mkdirs();
        }
        destFile.createNewFile();
        OutputStream out = new FileOutputStream(destFile);
        byte[] cache = new byte[CACHE_SIZE];
        int nRead = 0;
        while ((nRead = in.read(cache)) != -1) {
            out.write(cache, 0, nRead);
            out.flush();
        }
        out.close();
        in.close();
    }


    /**
     * 转换本地图片或网络图片为base64
     *
     * @param imagePath
     * @param fileSize
     * @return
     */
    public static String toBase64(String imagePath, long fileSize) {
        String value;
        if (imagePath.startsWith("http")) {
            value = urlChangeBase64(imagePath, fileSize);
        } else {
            value = compressPicForScale(imagePath, fileSize);
        }
        return value;
    }

    /**
     * 根据指定大小压缩图片
     *
     * @param imagePath 源图片地址
     * @param fileSize  指定图片大小，单位kb
     * @return 压缩质量后的图片BASE64转码字符串
     */

    public static String compressPicForScale(String imagePath, long fileSize) {
        imagePath = fileProperties.getPath() + imagePath;
        byte[] data = null;
        try (InputStream in = new FileInputStream(imagePath);) {
            data = new byte[in.available()];
            in.read(data);
        } catch (IOException e) {
            return pic;
        }
        if (data.length < fileSize * 1024) {
            String encode = new String(Base64.encodeBase64(data));
            return encode.substring(encode.indexOf(",") + 1);
        }

        try {
            while (data.length > fileSize * 1024) {
                ByteArrayInputStream inputStream = new ByteArrayInputStream(data);
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream(data.length);
                /**
                 * Thumbnails.of(InputStream... inputStreams) 从流读入源;
                 * .scale(double scale) 按比例缩放，0~1缩小，1原比例，>1放大;
                 * .scale(doublescaleWidth, double scaleHeight) 长宽各自设置比例，会拉伸;
                 * .outputQuality(double quality) 质量0.0<=quality<=1.0;
                 * .toOutputStream(OutputStream os) 无返回，写入outputStream里;
                 *
                 */
                Thumbnails.of(inputStream).scale(accuracy)
                        .outputQuality(accuracy).toOutputStream(outputStream);
                data = outputStream.toByteArray();
            }
        } catch (Exception e) {
            logger.error("图片压缩失败=======", e);
            return pic;
        }


        String encode = new String(Base64.encodeBase64(data));
        return encode.substring(encode.indexOf(",") + 1);
    }

    /**
     * 下载网络图片并压缩到50kb
     *
     * @param imagePath 图片路径
     * @param fileSize
     * @return
     */
    public static String urlChangeBase64(String imagePath, long fileSize) {
        String base64 = "";
        byte[] bytes = HttpUtil.downloadBytes(imagePath);
        if (bytes.length > 0) {
            byte[] picCycle = ImageUtil.compressPicCycle(bytes, fileSize, 0.9,true);
            if (picCycle.length >= IMAGE_SIZE) {
                byte[] defaultBytes = HttpUtil.downloadBytes(DEFAULT_IMAGE_URL);
                base64 = Base64.encodeBase64String(defaultBytes);
            } else {
                base64 = Base64.encodeBase64String(picCycle);
            }
        }
        return base64;
    }

    /**
     * 将BufferedImage转换为base64字符串
     *
     * @param bufferedImage
     * @return
     */
    public static String imageToBase64(BufferedImage bufferedImage) {
        Base64 encoder = new Base64();
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            ImageIO.write(bufferedImage, "png", baos);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return new String(encoder.encode((baos.toByteArray())));
    }
}
