package io.renren.utils;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.SmzApiUrl;
import io.renren.common.exception.RenException;
import io.renren.common.redis.RedisUtils;
import io.renren.dao.ZBankAccDao;
import io.renren.entity.ZBankAccEntity;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * <AUTHOR>
 * @title SmzApiRequestUtil
 * @Description 住建实名制API请求工具类
 * @Date 2020/4/10 15:32
 * @Copyright 2019-2025
 */
@Component
@EnableScheduling
public class SmzApiRequestUtil {
    private final Logger logger = LoggerFactory.getLogger(SmzApiRequestUtil.class);
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private SmzApiUrl smzApiUrl;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private ZBankAccDao zBankAccDao;
    private static final String SUCCESS_STATUS = "0";
    private static final String STATUS_VALUE = "code";
    private static final String TOKEN_EXPIRY = "10021";
    private static final String ERROR_CODE = "500";


    /**
     * 初始化token信息
     */
    @Scheduled(initialDelay = 1000, fixedDelayString = "********")
    public void initialToken() {
        logger.info("获取token！");
        QueryWrapper<ZBankAccEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("WHETHER", "1");
        List<ZBankAccEntity> zBankAccEntities = zBankAccDao.selectList(wrapper);
        zBankAccEntities.forEach(bankDataEntry -> {
            JSONObject requestParameter = new JSONObject();
            requestParameter.put("secretkey", bankDataEntry.getSecretkey());
            requestParameter.put("username", bankDataEntry.getUsername());
            requestParameter.put("password", bankDataEntry.getPassword());
            JSONObject result = restTemplate.postForEntity(smzApiUrl.getLogin(), requestParameter, JSONObject.class).getBody();
            if (!SUCCESS_STATUS.equals(result != null ? result.getString(STATUS_VALUE) : null)) {
                logger.error("获取token失败,返回结果:" + result);
                throw new RenException("获取token失败！返回结果:" + result);
            }
            String token = result.getJSONObject("data").getString("token");
            bankDataEntry.setToken(token);
            redisUtils.set(bankDataEntry.getCode(), bankDataEntry);
            redisUtils.set(bankDataEntry.getUsername(), token);
        });
    }

    /**
     * @description 上报数据返回correlation_id
     * <AUTHOR>
     * @date 2020年04月13日 10:44
     */
    public JSONObject uploadData(String bankCode, Object requestParameter, String url) {
        String token = getToken(bankCode);
        JSONObject result = new JSONObject();
        if (StringUtils.isNotEmpty(token)) {
            result = restTemplate.postForObject(url + "?token=" + token, requestParameter, JSONObject.class);
            String statusValue = result != null ? result.getString(STATUS_VALUE) : null;
            if (!SUCCESS_STATUS.equals(statusValue) && !ERROR_CODE.equals(statusValue)) {
                if (TOKEN_EXPIRY.equals(statusValue)) {
                    initialToken();
                } else {
                    logger.error("上报数据失败！返回结果:" + result);
                }
            }
        }
        return result;
    }

    /**
     * 上报基础信息
     *
     * @param bankCode         银行代码
     * @param requestParameter Object参数
     * @param url              地址
     * @return JSONObject
     */
    public JSONObject uploadBaseData(String bankCode, Object requestParameter, String url) {
        String token = getToken(bankCode);
        JSONObject result = new JSONObject();
        if (StringUtils.isNotEmpty(token)) {
            result = restTemplate.postForObject(url + "?token=" + token, requestParameter, JSONObject.class);
            String statusValue = result != null ? result.getString(STATUS_VALUE) : null;
            if (!SUCCESS_STATUS.equals(statusValue) && !ERROR_CODE.equals(statusValue)) {
                if (TOKEN_EXPIRY.equals(statusValue)) {
                    initialToken();
                } else {
                    logger.error("上报数据失败！返回结果:" + result);
                }
            }
        }
        return result;
    }

    private String getToken(String bankCode) {
        String lockKey = "voucher_lock_" + bankCode;
        if (redisUtils.get(lockKey) != null) {
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        } else {
            redisUtils.set(lockKey, "1", 2);
        }
        Object bean = redisUtils.get(bankCode);
        if (ObjectUtil.isEmpty(bean)) {
            this.initialToken();
            bean = redisUtils.get(bankCode);
        }
        ZBankAccEntity bankDataEntry = Convert.convert(ZBankAccEntity.class, bean);
        return bankDataEntry.getToken();
    }
}
