package io.renren.utils;

import io.renren.common.DictionaryConversionContrast;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 字典值转换
 */
@Component
public class TransformUtils {

    public static String dictionary(String value,String type){
        Map<String,String> map = DictionaryConversionContrast.dictionaryConversionMap
                .get(type);
        String really = map.get(value);
        if (StringUtils.isBlank(really)){
            really = value;
        }
        return really;
    }
}
