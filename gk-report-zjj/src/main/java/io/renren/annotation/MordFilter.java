package io.renren.annotation;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.serializer.ValueFilter;
import io.renren.utils.AesUtils;
import io.renren.utils.Base64Utils;
import io.renren.utils.TransformUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;

/**
 * 自定义字段过滤器，处理json数据格式
 *
 * <AUTHOR>
 * @Date 2020-11-27 13:27
 */

public class MordFilter implements ValueFilter {

    @Override
    public Object process(Object object, String name, Object value) {
        if (ObjectUtil.isNull(object) || StringUtils.isEmpty(name) || ObjectUtil.isNull(value)) {
            return value;
        }
        try {
            String bankcode = (String) ReflectUtil.getFieldValue(object, "bankcode");
            Field field = object.getClass().getDeclaredField(name);
            boolean isEncryption = field.isAnnotationPresent(IsEncryption.class);
            boolean urlToBase64Field = field.isAnnotationPresent(UrlToBase64Field.class);
            boolean isConversionDic = field.isAnnotationPresent(IsConversionDic.class);
            //此处目前就2个注解,如果多了可以进行优化，不要用if判断
            if (isEncryption) {
                String plaintextValue = (String) value;
                value = AesUtils.encrypt(plaintextValue, bankcode);
            }
            if (urlToBase64Field) {
                String plaintextValue = (String) value;
                value = Base64Utils.toBase64(plaintextValue, 50 * 1024);
            }
            if (isConversionDic) {
                String type = field.getAnnotation(IsConversionDic.class).type();
                String plaintextValue = (String) value;
                value = TransformUtils.dictionary(plaintextValue, type);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return value;
    }
}
