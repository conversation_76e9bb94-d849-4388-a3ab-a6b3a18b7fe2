package io.renren.controller;

import io.renren.service.ReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2022-03-30 15:55
 */
@Component
public class ReportData {
    @Autowired
    private ReportService service;

    /**
     * 项目信息
     */
    @Scheduled(cron = "0 0/5 * * * ?")
    public void reportProject() {
        service.reportProject();
    }

    /**
     * 施工许可证
     */
    /*@Scheduled(cron = "0 0/5 * * * ?")
    public void reportBuilderLicense() {
        service.reportBuilderLicense();
    }*/

    /**
     * 人员基础信息
     */
    @Scheduled(cron = "0 0/5 * * * ?")
    public void reportPerson() {
        service.reportPerson();
    }

    /**
     * 企业基础信息
     */
    @Scheduled(initialDelay = 5000, fixedRate = 10000)
    public void reportCompany() {
        service.reportCompany();
    }

    /**
     * 参建单位信息
     */
    @Scheduled(initialDelay = 5000, fixedRate = 10000)
    public void reportConstruction() {
        service.reportConstruction();
    }

    /**
     * 上报班组信息
     */
    @Scheduled(cron = "0 0/5 * * * ?")
    public void reportTeam() {
        service.reportTeam();
    }

    /**
     * 上报工人信息
     */
    @Scheduled(cron = "0 0/1 * * * ?")
    public void reportWorker() {
        service.reportWorker();
    }

    /**
     * 上报管理人员信息
     */
    @Scheduled(cron = "0 0/1 * * * ?")
    public void reportManager() {
        service.reportManager();
    }

    /**
     * 上报工人考勤
     */
    @Scheduled(cron = "0 0/2 * * * ?")
    public void reportWorkerAttendance() {
        service.reportWorkerAttendance();
    }

    /**
     * 上报管理人员考勤
     */
    @Scheduled(cron = "0 0/2 * * * ?")
    public void reportManagerAttendance() {
        service.reportManagerAttendance();
    }

    /**
     * 获取项目省级编码
     */
//    @Scheduled(cron = "0 0 */10 * * ?")
//    public void getProvincialCode() {
//        service.provincialCode();
//    }

    /**
     * 获取参建单位审核状态
     */
    @Scheduled(cron = "0 0/10 * * * ?")
    public void partApprovalStatus() {
        service.partApprovalStatus();
    }

    /**
     * 获取管理人员审核状态
     */
    @Scheduled(cron = "0 0/10 * * * ?")
    public void managerApprovalStatus() {
        service.managerApprovalStatus();
    }

    /**
     * 上报工人退场数据
     */
    @Scheduled(cron = "0 */1 * * * ?")
    public void reportWokerExit() {
        service.reportWokerExit();
    }

    /**
     * 上报管理人员退场数据
     */
    @Scheduled(cron = "0 */1 * * * ?")
    public void reportManagerExit() {
        service.reportManagerExit();
    }

}
