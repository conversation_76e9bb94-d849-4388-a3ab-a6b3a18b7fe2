package io.renren.dto;

import io.renren.annotation.IsEncryption;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 项目基础信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-04
 */
@Data
@ApiModel(value = "项目基础信息表")
public class Pj01DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    private Long pj0101;
    @ApiModelProperty(value = "安监备案号")
    private String safetyno;
    @ApiModelProperty(value = "项目编码")
    private String code;
    @ApiModelProperty(value = "项目名称")
    private String name;
    @ApiModelProperty(value = "项目简介")
    private String description;
    @ApiModelProperty(value = "所属行业")
    private String industry;
    @ApiModelProperty(value = "项目类别")
    private String category;
    @ApiModelProperty(value = "建设性质")
    private String constructtype;
    @ApiModelProperty(value = "投资类型")
    private String investtype;
    @ApiModelProperty(value = "项目所在地")
    private String areacode;
    @ApiModelProperty(value = "建设地址（项目的详细地址具体到XX街道XX号）")
    private String address;
    @ApiModelProperty(value = "总面积(平方米)")
    private BigDecimal buildingarea;
    @ApiModelProperty(value = "总长度(米)")
    private BigDecimal buildinglength;
    @ApiModelProperty(value = "总投资(万元)")
    private BigDecimal invest;
    @ApiModelProperty(value = "工程造价(万元)")
    private BigDecimal engineering;
    @ApiModelProperty(value = "项目规模")
    private String scale;
    @ApiModelProperty(value = "开工日期")
    private Date startdate;
    @ApiModelProperty(value = "竣工日期")
    private Date completeDate;
    @ApiModelProperty(value = "经度")
    private BigDecimal lng;
    @ApiModelProperty(value = "纬度")
    private BigDecimal lat;
    @ApiModelProperty(value = "联系人姓名")
    private String linkman;
    @ApiModelProperty(value = "联系人电话")
    private String linkphone;
    @ApiModelProperty(value = "是否重点项目")
    private String ismajorProject;
    @ApiModelProperty(value = "是否缴纳保证金")
    private String isdeposit;
    @ApiModelProperty(value = "项目状态")
    private String prjstatus;
    @ApiModelProperty(value = "机构ID")
    private Long deptId;
    @ApiModelProperty(value = "住建局上报状态")
    private String stupstatus;
    @ApiModelProperty(value = "标注位置（例如大门、项目部等）")
    private String markLocation;
    @ApiModelProperty(value = "中心负责人id")
    private Long ps1201;
    @ApiModelProperty(value = "电子围栏区域")
    private String region;
    @ApiModelProperty(value = "是否上报")
    private String whetherReport;
    /*@ApiModelProperty(value = "银行代码")
    private String bankCode;*/

    private Date completedate;

    private String prjsize;

    private String propertynum;

    private String virareacode;

    private String corpcode;

    private Date entrytime;

    @IsEncryption
    private String banknumber;

    private String banknumbername;

    private String bankname;

    private String payrolltopbankcode;

    private String bankcode;

}