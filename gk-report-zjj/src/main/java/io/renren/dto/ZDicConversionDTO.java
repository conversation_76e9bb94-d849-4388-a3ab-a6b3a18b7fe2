package io.renren.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 泸州住建上报字典转化
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-04
 */
@Data
@ApiModel(value = "泸州住建上报字典转化")
public class ZDicConversionDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "类型")
    private String type;
    @ApiModelProperty(value = "实际值")
    private String actualValue;
    @ApiModelProperty(value = "转换值")
    private String conversionValue;

}