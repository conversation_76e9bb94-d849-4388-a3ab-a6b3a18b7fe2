package io.renren.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 工资专户
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@ApiModel(value = "工资专户")
public class Pa01DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long pa0101;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "农民工工资专用账户名称", required = true)
    @NotBlank(message = "农民工工资专用账户名称不能为空")
    @Length(max = 25, message = "农民工工资专用账户名称过长，不能超过25个汉字")
    private String specialAccount;

    @ApiModelProperty(value = "农民工工资专用账户开户行名称", required = true)
    @NotBlank(message = "农民工工资专用账户开户行名称不能为空")
    @Length(max = 25, message = "农民工工资专用账户开户行名称过长，不能超过25个汉字")
    private String bankName;

    @ApiModelProperty(value = "农民工工资专用账户账号", required = true)
    @NotBlank(message = "农民工工资专用账户账号不能为空")
    private String specialAccountNo;

    @ApiModelProperty(value = "银行代码", required = true)
    @NotBlank(message = "银行代码不能为空")
    private String payBankCode;


}