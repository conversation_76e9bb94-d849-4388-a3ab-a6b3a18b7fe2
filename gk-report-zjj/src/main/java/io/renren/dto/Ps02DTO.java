package io.renren.dto;

import io.renren.annotation.IsConversionDic;
import io.renren.annotation.IsEncryption;
import io.renren.annotation.UrlToBase64Field;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 建筑工人信息
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-05
 */
@Data
@ApiModel(value = "建筑工人信息")
public class Ps02DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long ps0201;
    @ApiModelProperty(value = "人员ID")
    private Long ps0101;
    @ApiModelProperty(value = "项目ID")
    private Long pj0101;
    @ApiModelProperty(value = "班组ID")
    private Long tm0101;
    @ApiModelProperty(value = "是否班组长")
    private String isteamleader;
    @IsConversionDic(type = "WORKTYPECODE")
    @ApiModelProperty(value = "工种")
    private String worktypecode;
    @IsEncryption
    @ApiModelProperty(value = "工资卡帐号")
    private String payrollbankcardnumber;
    @ApiModelProperty(value = "工资卡开户行名称")
    private String payrollbankname;
    @ApiModelProperty(value = "工资卡银行代码")
    private String payrolltopbankcode;
    @ApiModelProperty(value = "工人头像")
    @UrlToBase64Field
    private String issuecardpicurl;
    @ApiModelProperty(value = "是否购买工伤或意外伤害保险 ")
    private String hasbuyinsurance;
    @ApiModelProperty(value = "进场时间")
    private Date entrytime;
    @ApiModelProperty(value = "退场时间")
    private Date exittime;
    @ApiModelProperty(value = "进退场状态")
    private String inOrOut;
    @ApiModelProperty(value = "备注")
    private String memo;
    @ApiModelProperty(value = "工资卡支付行号")
    private String payrollno;
    @ApiModelProperty(value = "住建局上报状态")
    private String stupstatus;
    @ApiModelProperty(value = "住建局上报结果")
    private String upmsg;

    private String corpcode;
    private String teamsysno;
    @IsEncryption
    private String idcardnumber;
    private String hascontract;
    @IsEncryption
    private String paybankcardnumber;
    private String paybankname;
    private String paybankcode;
    private String bankcode;
    private String projectcode;
}