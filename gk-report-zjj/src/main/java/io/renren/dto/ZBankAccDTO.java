package io.renren.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 泸州住建上报银行账户
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-04
 */
@Data
@ApiModel(value = "泸州住建上报银行账户")
public class ZBankAccDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "银行代码")
    private String code;
    @ApiModelProperty(value = "账号")
    private String username;
    @ApiModelProperty(value = "密码")
    private String password;
    @ApiModelProperty(value = "密钥")
    private String secretkey;
    @ApiModelProperty(value = "token")
    private String token;
    @ApiModelProperty(value = "是否可用(0否,1是)")
    private String whether;

}