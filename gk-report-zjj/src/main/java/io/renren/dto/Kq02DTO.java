package io.renren.dto;

import io.renren.annotation.IsEncryption;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 建筑工人考勤信息
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-06
 */
@Data
@ApiModel(value = "建筑工人考勤信息")
public class Kq02DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long kq0201;
    @ApiModelProperty(value = "人员ID")
    private Long userId;
    @ApiModelProperty(value = "姓名")
    private String personName;
    @ApiModelProperty(value = "人员类型")
    private String personType;
    @ApiModelProperty(value = "项目ID")
    private Long pj0101;
    @ApiModelProperty(value = "设备序列号")
    private String deviceserialno;
    @ApiModelProperty(value = "考勤时间")
    private Date checkdate;
    @ApiModelProperty(value = "进出方向(1进场，2离场)")
    private String direction;
    @ApiModelProperty(value = "通行方式")
    private String attendtype;
    @ApiModelProperty(value = "经度")
    private BigDecimal lng;
    @ApiModelProperty(value = "纬度")
    private BigDecimal lat;
    @ApiModelProperty(value = "刷卡近照")
    private String imageUrl;
    @ApiModelProperty(value = "1为补卡的考勤记录")
    private BigDecimal ismakecard;
    @ApiModelProperty(value = "班组ID")
    private Long tm0101;
    @ApiModelProperty(value = "住建局上报状态")
    private String stupstatus;

    private String projectcode;
    private String corpcode;
    private String teamsysno;
    @IsEncryption
    private String idcardnumber;
    private String bankcode;
}