package io.renren.dto;

import io.renren.annotation.IsConversionDic;
import io.renren.annotation.IsEncryption;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 人员实名基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-04
 */
@Data
@ApiModel(value = "人员实名基础信息")
public class Ps01DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long ps0101;
    @ApiModelProperty(value = "姓名")
    private String name;
    @ApiModelProperty(value = "证件类型")
    private String idcardtype;
    @IsEncryption
    @ApiModelProperty(value = "证件号码")
    private String idcardnumber;
    @IsConversionDic(type = "GENDER")
    @ApiModelProperty(value = "性别")
    private String gender;
    @IsConversionDic(type = "NATION")
    @ApiModelProperty(value = "民族")
    private String nation;
    @ApiModelProperty(value = "出生日期")
    private Date birthday;
    @ApiModelProperty(value = "住址")
    private String address;
    @ApiModelProperty(value = "学历")
    private String edulevel;
    @ApiModelProperty(value = "学位")
    private String degree;
    @ApiModelProperty(value = "人员类别（职员，工人）(dic)")
    private String workertype;
    @ApiModelProperty(value = "籍贯（身份证号前6位）")
    private String areacode;
    @ApiModelProperty(value = "身份证头像")
    private String headimageurl;
    @ApiModelProperty(value = "政治面貌")
    private String politicstype;
    @ApiModelProperty(value = "是否加入工会")
    private String isjoined;
    @ApiModelProperty(value = "加入工会时间")
    private Date joinedtime;
    @ApiModelProperty(value = "手机号码")
    private String cellphone;
    @ApiModelProperty(value = "文化程度")
    private String cultureleveltype;
    @ApiModelProperty(value = "特长")
    private String specialty;
    @ApiModelProperty(value = "是否有重大病史")
    private String hasbadmedicalhistory;
    @ApiModelProperty(value = "紧急联系人姓名")
    private String urgentlinkman;
    @ApiModelProperty(value = "紧急联系电话")
    private String urgentlinkmanphone;
    @ApiModelProperty(value = "开始工作日期")
    private Date workdate;
    @ApiModelProperty(value = "婚姻状况")
    private String maritalstatus;
    @ApiModelProperty(value = "发证机关")
    private String grantorg;
    @ApiModelProperty(value = "正面照 URL")
    private String positiveidcardimageurl;
    @ApiModelProperty(value = "反面照 URL")
    private String negativeidcardimageurl;
    @ApiModelProperty(value = "有效期开始日期")
    private Date startdate;
    @ApiModelProperty(value = "有效期结束日期")
    private Date expirydate;
    @ApiModelProperty(value = "住建局上报状态")
    private String stupstatus;
    @ApiModelProperty(value = "住建局上报结果")
    private String upmsg;

    private String bankCode;

}