package io.renren.dto;

import io.renren.annotation.IsEncryption;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 班组基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-05
 */
@Data
@ApiModel(value = "班组基础信息")
public class Tm01DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long tm0101;
    @ApiModelProperty(value = "参建单位ID")
    private Long cp0201;
    @ApiModelProperty(value = "项目ID")
    private Long pj0101;
    @ApiModelProperty(value = "班组编号")
    private String teamsysno;
    @ApiModelProperty(value = "班组名称")
    private String teamname;
    @ApiModelProperty(value = "责任人姓名")
    private String responsiblepersonname;
    @ApiModelProperty(value = "责任人联系电话")
    private String responsiblepersonphone;
    @ApiModelProperty(value = "责任人证件类型")
    private String idcardtype;
    @IsEncryption
    @ApiModelProperty(value = "责任人证件号码")
    private String responsiblepersonidnumber;
    @ApiModelProperty(value = "进场时间")
    private Date entrytime;
    @ApiModelProperty(value = "退场时间")
    private Date exittime;
    @ApiModelProperty(value = "进退场状态")
    private String inOrOut;
    @ApiModelProperty(value = "备注")
    private String memo;
    @ApiModelProperty(value = "住建局上报状态")
    private String stupstatus;
    private String upmsg;


    private String projectcode;
    private String corpcode;
    private String bankcode;

}