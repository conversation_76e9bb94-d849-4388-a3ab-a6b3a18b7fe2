package io.renren.dto;

import io.renren.annotation.IsConversionDic;
import io.renren.annotation.IsEncryption;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 管理人员参建信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-06
 */
@Data
@ApiModel(value = "管理人员参建信息表")
public class Ps04DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long ps0401;
    @ApiModelProperty(value = "项目ID")
    private Long pj0101;
    @ApiModelProperty(value = "参建单位ID")
    private Long cp0201;
    @ApiModelProperty(value = "人员ID")
    private Long ps0301;
    @IsConversionDic(type = "JOBTYPE")
    @ApiModelProperty(value = "岗位类型")
    private String jobtype;
    @ApiModelProperty(value = "项目采集照片")
    private String photo;
    @ApiModelProperty(value = "进场时间")
    private Date entrytime;
    @ApiModelProperty(value = "退场时间")
    private Date exittime;
    @ApiModelProperty(value = "进退场状态")
    private String inOrOut;
    @ApiModelProperty(value = "住建局上报状态")
    private String stupstatus;
    @ApiModelProperty(value = "资质确认状态")
    private String isconfirm;
    @ApiModelProperty(value = "资质确认结果")
    private String confirmresult;
    @ApiModelProperty(value = "确认人")
    private String confirmor;
    @ApiModelProperty(value = "确认时间")
    private Date confirmDate;
    @ApiModelProperty(value = "住建局上报结果")
    private String upmsg;
    @ApiModelProperty(value = "住建局审核状态")
    private String auditstatus;
    @ApiModelProperty(value = "住建局审核结果")
    private String auditmsg;

    private String projectcode;
    private String corpcode;
    @IsEncryption
    private String idcardnumber;
    private String hascontract;
    private String hasbuyinsurance;
    private String bankcode;
}