package io.renren.dto;

import io.renren.annotation.IsEncryption;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 参建单位信息
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-05
 */
@Data
@ApiModel(value = "参建单位信息")
public class Cp02DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long cp0201;
    @ApiModelProperty(value = "企业ID")
    private Long cp0101;
    @ApiModelProperty(value = "项目ID")
    private Long pj0101;
    @ApiModelProperty(value = "参建类型")
    private String corptype;
    @ApiModelProperty(value = "进场时间")
    private Date entrytime;
    @ApiModelProperty(value = "退场时间")
    private Date exittime;
    @ApiModelProperty(value = "进退场状态")
    private String inOrOut;
    @ApiModelProperty(value = "住建局上报状态")
    private String stupstatus;

    private String upmsg;
    private String auditstatus;
    private String auditmsg;


    private String projectcode;
    private String corpcode;
    private String corpintype;
    @IsEncryption
    private String banknumber;
    private String banknumbername;
    private String bankname;
    private String payrolltopbankcode;
    private String bankcode;





}