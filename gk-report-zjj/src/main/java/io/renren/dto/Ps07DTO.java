package io.renren.dto;

import io.renren.annotation.IsEncryption;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 项目管理人员进退场信息
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-07
 */
@Data
@ApiModel(value = "项目管理人员进退场信息")
public class Ps07DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long ps0701;
    @ApiModelProperty(value = "管理人员主键ID")
    private Long ps0401;
    @ApiModelProperty(value = "进退场时间")
    private Date entryOrExitTime;
    @ApiModelProperty(value = "进退场状态")
    private String inOrOut;
    @ApiModelProperty(value = "备注")
    private String memo;
    @ApiModelProperty(value = "住建局上报状态")
    private String stupstatus;
    @ApiModelProperty(value = "住建局上报结果")
    private String upmsg;

    private String projectcode;
    private Date exittime;
    @IsEncryption
    private String idcardnumber;
    private String bankcode;
}