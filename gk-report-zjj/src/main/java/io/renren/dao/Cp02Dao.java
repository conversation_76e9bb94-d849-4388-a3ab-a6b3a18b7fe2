package io.renren.dao;

import io.renren.common.dao.BaseDao;
import io.renren.dto.Cp02DTO;
import io.renren.entity.Cp02Entity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 参建单位信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-08-05
 */
@Mapper
public interface Cp02Dao extends BaseDao<Cp02Entity> {
    /**
     * @description: 查询需要上报的参建单位
     * @author: CJF
     * @date: 2025/8/5 9:58
     * @param:
     * @return:
     **/
    Cp02DTO getNeedUpData();
    /**
     * @description: 插叙需要获取审核状态的参建单位
     * @author: CJF
     * @date: 2025/8/7 13:34
     * @param:
     * @return:
     **/
    List<Cp02DTO> getNeedStatus();
}