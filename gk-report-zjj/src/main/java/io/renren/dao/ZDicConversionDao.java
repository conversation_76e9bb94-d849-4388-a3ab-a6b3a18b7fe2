package io.renren.dao;

import io.renren.common.dao.BaseDao;
import io.renren.dto.ZDicConversionDTO;
import io.renren.entity.ZDicConversionEntity;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 泸州住建上报字典转化
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-08-04
 */
@Mapper
public interface ZDicConversionDao extends BaseDao<ZDicConversionEntity> {

    List<String> selectConversionType();

    @MapKey("actualValue")
    Map<String, ZDicConversionDTO> selectByTypeToMap(String type);
}