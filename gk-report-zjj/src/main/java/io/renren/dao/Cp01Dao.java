package io.renren.dao;

import io.renren.common.dao.BaseDao;
import io.renren.dto.Cp01DTO;
import io.renren.entity.Cp01Entity;
import org.apache.ibatis.annotations.Mapper;

/**
 * 企业基础信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-08-04
 */
@Mapper
public interface Cp01Dao extends BaseDao<Cp01Entity> {
    /**
     * @description: 查询需要上报的企业基本信息
     * @author: CJF
     * @date: 2025/8/4 15:26
     * @param:
     * @return:
     **/
    Cp01DTO getNeedUpData();
}