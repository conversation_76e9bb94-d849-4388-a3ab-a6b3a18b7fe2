package io.renren.dao;

import io.renren.common.dao.BaseDao;
import io.renren.dto.Ps07DTO;
import io.renren.entity.Ps07Entity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 项目管理人员进退场信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-08-07
 */
@Mapper
public interface Ps07Dao extends BaseDao<Ps07Entity> {
    /**
     * @description: 获取需要上报的管理人员退场信息
     * @author: CJF
     * @date: 2025/8/8 10:40
     * @param:
     * @return:
     **/
    List<Ps07DTO> getNeedManagerExit();
}