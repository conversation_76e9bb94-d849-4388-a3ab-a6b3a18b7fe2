package io.renren.dao;

import io.renren.common.dao.BaseDao;
import io.renren.dto.Ps04DTO;
import io.renren.entity.Ps04Entity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 管理人员参建信息表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-08-06
 */
@Mapper
public interface Ps04Dao extends BaseDao<Ps04Entity> {
    /**
     * @description: 查询需要上报的管理人员
     * @author: CJF
     * @date: 2025/8/6 9:53
     * @param:
     * @return:
     **/
    List<Ps04DTO> getNeedUpData();
    /**
     * @description: 批量修改
     * @author: CJF
     * @date: 2025/8/6 14:38
     * @param:
     * @return:
     **/
    void batchUpdate(List<Ps04DTO> list);
    /**
     * @description: 查询需要获取审核状态的管理人员
     * @author: CJF
     * @date: 2025/8/7 14:13
     * @param:
     * @return:
     **/
    List<Ps04DTO> getNeedStatus();
}