package io.renren.dao;

import io.renren.common.dao.BaseDao;
import io.renren.dto.Kq02DTO;
import io.renren.entity.Kq02Entity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 建筑工人考勤信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-08-06
 */
@Mapper
public interface Kq02Dao extends BaseDao<Kq02Entity> {
    /**
     * @description: 查询需要上报的工人考勤
     * @author: CJF
     * @date: 2025/8/6 15:30
     * @param:
     * @return:
     **/
    List<Kq02DTO> getNeedUpWokerData();
    /**
     * @description: 批量修改上报状态
     * @author: CJF
     * @date: 2025/8/6 16:34
     * @param:
     * @return:
     **/
    void batchUpdate(List<Kq02DTO> list);
    /**
     * @description: 查询需要上报的管理人员考勤
     * @author: CJF
     * @date: 2025/8/7 8:44
     * @param:
     * @return:
     **/
    List<Kq02DTO> getNeedUpManagerData();
}