package io.renren.dao;

import io.renren.common.dao.BaseDao;
import io.renren.dto.Ps02DTO;
import io.renren.entity.Ps02Entity;
import org.apache.ibatis.annotations.Mapper;

import java.util.ArrayList;
import java.util.List;

/**
 * 建筑工人信息
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-05
 */
@Mapper
public interface Ps02Dao extends BaseDao<Ps02Entity> {
    /**
     * @description: 查询需要上报的工人
     * @author: CJF
     * @date: 2025/8/5 16:32
     * @param:
     * @return:
     **/
    List<Ps02DTO> getNeedUpData();
    /**
     * @description: 批量修改
     * @author: CJF
     * @date: 2025/8/6 9:13
     * @param:
     * @return:
     **/
    void batchUpdate(List<Ps02DTO> list);
}