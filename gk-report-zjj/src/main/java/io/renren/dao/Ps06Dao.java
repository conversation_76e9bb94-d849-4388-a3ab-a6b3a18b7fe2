package io.renren.dao;

import io.renren.common.dao.BaseDao;
import io.renren.dto.Ps06DTO;
import io.renren.entity.Ps06Entity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 工人进退场信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-08-07
 */
@Mapper
public interface Ps06Dao extends BaseDao<Ps06Entity> {
    /**
     * @description: 查询需要上报的工人退场信息
     * @author: CJF
     * @date: 2025/8/7 16:35
     * @param:
     * @return:
     **/
    List<Ps06DTO> getNeedWokerExit();
}