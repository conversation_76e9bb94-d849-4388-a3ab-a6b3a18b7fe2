package io.renren.dao;

import io.renren.common.dao.BaseDao;
import io.renren.dto.Ps01DTO;
import io.renren.entity.Ps01Entity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 人员实名基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-04
 */
@Mapper
public interface Ps01Dao extends BaseDao<Ps01Entity> {
    /**
     * @description: 查询需要上报的人员基础信息
     * @author: CJF
     * @date: 2025/8/4 14:05
     * @param:
     * @return:
     **/
    List<Ps01DTO> getNeedUpData();
    /**
     * @description: 批量更新
     * @author: CJF
     * @date: 2025/8/4 15:12
     * @param:
     * @return:
     **/
    void batchUpdate(List<Ps01DTO> list);
}