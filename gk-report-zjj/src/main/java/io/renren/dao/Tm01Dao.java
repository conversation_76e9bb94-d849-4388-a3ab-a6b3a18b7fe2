package io.renren.dao;

import io.renren.common.dao.BaseDao;
import io.renren.dto.Tm01DTO;
import io.renren.entity.Tm01Entity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 班组基础信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-08-05
 */
@Mapper
public interface Tm01Dao extends BaseDao<Tm01Entity> {
    /**
     * @description: 查询需要上报的班组数据
     * @author: CJF
     * @date: 2025/8/5 13:34
     * @param:
     * @return:
     **/
    List<Tm01DTO> getNeedUpData();
    /**
     * @description: 批量更新
     * @author: CJF
     * @date: 2025/8/5 15:57
     * @param:
     * @return:
     **/
    void batchUpdate(List<Tm01Entity> list);
}