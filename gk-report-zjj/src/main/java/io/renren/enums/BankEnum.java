package io.renren.enums;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * <AUTHOR>
 */

public enum BankEnum {
    //泸州农商银行
    LZNXYH("402", "lznxyh", "bYxoP74O", "92E2F30599B7F780E6BBEE77A16BB105"),
    //泸州邮储银行
    LZYCYH("100", "lzycyh", "MIwv6DXg", "5ECC6115B387D8719C0E1667900F8CD6"),
    //建设银行
    LZJSYH("105", "lzjsyh02", "4IUriM2D", "D24E9E5738C01D6288431834EAE2E392"),
    //中信银行
    LZZXYH("302", "lzzxyh02", "dP5MV0sW", "PT3VIA7IKWQHNT1YD0JD8N6B3JTQK6NQ"),
    //泸州农业发展银行
    LZNYFZYH("203", "lznyfzyh02", "dP5MV0sW", "545268A24CB146311D59432C481H733P");
    public static final Map<String, BankEnum> lookup = new HashMap<>();
    public final String code;
    public final String account;
    public final String password;
    public final String secretkey;

    BankEnum(String code, String account, String password, String secretkey) {
        this.code = code;
        this.account = account;
        this.password = password;
        this.secretkey = secretkey;
    }

    public static BankEnum get(String code) {
        return lookup.get(code);
    }

    static {
        Iterator<BankEnum> iterator = EnumSet.allOf(BankEnum.class).iterator();
        while (iterator.hasNext()) {
            BankEnum next = iterator.next();
            lookup.put(next.code, next);
        }
    }
}
