package io.renren.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 管理人员参建信息表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-08-06
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_PS04")
public class Ps04Entity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
	@TableId
	private Long ps0401;
    /**
     * 项目ID
     */
	private Long pj0101;
    /**
     * 参建单位ID
     */
	private Long cp0201;
    /**
     * 人员ID
     */
	private Long ps0301;
    /**
     * 岗位类型
     */
	private String jobtype;
    /**
     * 项目采集照片
     */
	private String photo;
    /**
     * 进场时间
     */
	private Date entrytime;
    /**
     * 退场时间
     */
	private Date exittime;
    /**
     * 进退场状态
     */
	private String inOrOut;
    /**
     * 住建局上报状态
     */
	private String stupstatus;
    /**
     * 资质确认状态
     */
	private String isconfirm;
    /**
     * 资质确认结果
     */
	private String confirmresult;
    /**
     * 确认人
     */
	private String confirmor;
    /**
     * 确认时间
     */
	private Date confirmDate;
    /**
     * 住建局上报结果
     */
	private String upmsg;
    /**
     * 住建局审核状态
     */
	private String auditstatus;
    /**
     * 住建局审核结果
     */
	private String auditmsg;
	/**
     * 创建者
     */
	private Long  creator;
	/**
     * 创建时间
     */
	private Date createDate;
	/**
	 * 更新者
	 */
	private Long updater;
	/**
	 * 更新时间
	 */
	private Date updateDate;
}