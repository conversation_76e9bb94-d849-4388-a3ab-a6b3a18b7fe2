package io.renren.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 泸州住建上报字典转化
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("Z_DIC_CONVERSION")
public class ZDicConversionEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 类型
     */
    private String type;
    /**
     * 实际值
     */
    private String actualValue;
    /**
     * 转换值
     */
    private String conversionValue;
}