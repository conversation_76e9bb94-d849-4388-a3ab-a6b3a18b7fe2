package io.renren.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 建筑工人考勤信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-08-06
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_KQ02")
public class Kq02Entity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
	@TableId
	private Long kq0201;
    /**
     * 人员ID
     */
	private Long userId;
    /**
     * 姓名
     */
	private String personName;
    /**
     * 人员类型
     */
	private String personType;
    /**
     * 项目ID
     */
	private Long pj0101;
    /**
     * 设备序列号
     */
	private String deviceserialno;
    /**
     * 考勤时间
     */
	private Date checkdate;
    /**
     * 进出方向(1进场，2离场)
     */
	private String direction;
    /**
     * 通行方式
     */
	private String attendtype;
    /**
     * 经度
     */
	private BigDecimal lng;
    /**
     * 纬度
     */
	private BigDecimal lat;
    /**
     * 刷卡近照
     */
	private String imageUrl;
    /**
     * 1为补卡的考勤记录
     */
	private BigDecimal ismakecard;
    /**
     * 班组ID
     */
	private Long tm0101;
    /**
     * 住建局上报状态
     */
	private String stupstatus;
	/**
     * 创建者
     */
	private Long  creator;
	/**
     * 创建时间
     */
	private Date createDate;
	/**
	 * 更新者
	 */
	private Long updater;
	/**
	 * 更新时间
	 */
	private Date updateDate;
}