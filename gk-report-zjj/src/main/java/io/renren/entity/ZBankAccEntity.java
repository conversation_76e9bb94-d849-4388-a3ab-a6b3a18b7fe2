package io.renren.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 泸州住建上报银行账户
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("Z_BANK_ACC")
public class ZBankAccEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 银行代码
     */
    private String code;
    /**
     * 账号
     */
    private String username;
    /**
     * 密码
     */
    private String password;
    /**
     * 密钥
     */
    private String secretkey;
    /**
     * token
     */
    private String token;
    /**
     * 是否可用(0否,1是)
     */
    private String whether;
}