package io.renren.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 企业基础信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-08-04
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_CP01")
public class Cp01Entity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
	@TableId
	private Long cp0101;
    /**
     * 社会统一信用代码
     */
	private String corpcode;
    /**
     * 企业名称
     */
	private String corpname;
    /**
     * 单位性质
     */
	private String organizationtype;
    /**
     * 注册地区
     */
	private String areacode;
    /**
     * 企业营业地址
     */
	private String address;
    /**
     * 邮政编码
     */
	private String zipcode;
    /**
     * 法定代表人姓名
     */
	private String legalman;
    /**
     * 法定代表人证件类型
     */
	private String idcardtype;
    /**
     * 法定代表人证件号码
     */
	private String legalmanidcardnumber;
    /**
     * 注册资本(万元)
     */
	private BigDecimal regcapital;
    /**
     * 注册资本币种
     */
	private String capitalcurrencytype;
    /**
     * 成立日期
     */
	private Date establishdate;
    /**
     * 办公电话
     */
	private String officephone;
    /**
     * 传真号码
     */
	private String faxnumber;
    /**
     * 联系人姓名
     */
	private String linkman;
    /**
     * 联系人办公电话
     */
	private String linkphone;
    /**
     * 联系人手机号码
     */
	private String linkcellphone;
    /**
     * 企业联系邮箱
     */
	private String email;
    /**
     * 企业网址
     */
	private String website;
    /**
     * 企业经营状态
     */
	private String businessstatus;
    /**
     * 经营范围
     */
	private String entscope;
    /**
     * 登记机关
     */
	private String regdept;
    /**
     * 企业备注
     */
	private String memo;
    /**
     * 机构Id
     */
	private Long deptId;
    /**
     * 住建局上报状态
     */
	private String stupstatus;
    /**
     * 住建局上报结果
     */
	private String upmsg;
	/**
     * 创建者
     */
	private Long  creator;
	/**
     * 创建时间
     */
	private Date createDate;
	/**
	 * 更新者
	 */
	private Long updater;
	/**
	 * 更新时间
	 */
	private Date updateDate;

	private String bankCode;
}