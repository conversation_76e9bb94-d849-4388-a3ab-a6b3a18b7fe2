package io.renren.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 参建单位信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-08-05
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_CP02")
public class Cp02Entity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
	@TableId
	private Long cp0201;
    /**
     * 企业ID
     */
	private Long cp0101;
    /**
     * 项目ID
     */
	private Long pj0101;
    /**
     * 参建类型
     */
	private String corptype;
    /**
     * 进场时间
     */
	private Date entrytime;
    /**
     * 退场时间
     */
	private Date exittime;
    /**
     * 进退场状态
     */
	private String inOrOut;
    /**
     * 住建局上报状态
     */
	private String stupstatus;
	/**
     * 创建者
     */
	private Long  creator;
	/**
     * 创建时间
     */
	private Date createDate;
	/**
	 * 更新者
	 */
	private Long updater;
	/**
	 * 更新时间
	 */
	private Date updateDate;

	private String upmsg;

	private String auditstatus;

	private String auditmsg;
}