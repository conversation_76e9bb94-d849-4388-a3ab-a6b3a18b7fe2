package io.renren.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 班组基础信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-08-05
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_TM01")
public class Tm01Entity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
	@TableId
	private Long tm0101;
    /**
     * 参建单位ID
     */
	private Long cp0201;
    /**
     * 项目ID
     */
	private Long pj0101;
    /**
     * 班组编号
     */
	private String teamsysno;
    /**
     * 班组名称
     */
	private String teamname;
    /**
     * 责任人姓名
     */
	private String responsiblepersonname;
    /**
     * 责任人联系电话
     */
	private String responsiblepersonphone;
    /**
     * 责任人证件类型
     */
	private String idcardtype;
    /**
     * 责任人证件号码
     */
	private String responsiblepersonidnumber;
    /**
     * 进场时间
     */
	private Date entrytime;
    /**
     * 退场时间
     */
	private Date exittime;
    /**
     * 进退场状态
     */
	private String inOrOut;
    /**
     * 备注
     */
	private String memo;
    /**
     * 住建局上报状态
     */
	private String stupstatus;
	/**
     * 创建者
     */
	private Long  creator;
	/**
     * 创建时间
     */
	private Date createDate;
	/**
	 * 更新者
	 */
	private Long updater;
	/**
	 * 更新时间
	 */
	private Date updateDate;

	private String upmsg;
}