package io.renren.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目管理人员进退场信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-08-07
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_PS07")
public class Ps07Entity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
	@TableId
	private Long ps0701;
    /**
     * 管理人员主键ID
     */
	private Long ps0401;
    /**
     * 进退场时间
     */
	private Date entryOrExitTime;
    /**
     * 进退场状态
     */
	private String inOrOut;
    /**
     * 备注
     */
	private String memo;
    /**
     * 住建局上报状态
     */
	private String stupstatus;
    /**
     * 住建局上报结果
     */
	private String upmsg;
	/**
     * 创建者
     */
	private Long  creator;
	/**
     * 创建时间
     */
	private Date createDate;
	/**
	 * 更新者
	 */
	private Long updater;
	/**
	 * 更新时间
	 */
	private Date updateDate;
}