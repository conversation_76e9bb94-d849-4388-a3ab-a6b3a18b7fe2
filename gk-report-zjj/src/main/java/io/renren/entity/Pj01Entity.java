package io.renren.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目基础信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_PJ01")
public class Pj01Entity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId
    private Long pj0101;
    /**
     * 安监备案号
     */
    private String safetyno;
    /**
     * 项目编码
     */
    private String code;
    /**
     * 项目名称
     */
    private String name;
    /**
     * 项目简介
     */
    private String description;
    /**
     * 所属行业
     */
    private String industry;
    /**
     * 项目类别
     */
    private String category;
    /**
     * 建设性质
     */
    private String constructtype;
    /**
     * 投资类型
     */
    private String investtype;
    /**
     * 项目所在地
     */
    private String areacode;
    /**
     * 建设地址（项目的详细地址具体到XX街道XX号）
     */
    private String address;
    /**
     * 总面积(平方米)
     */
    private BigDecimal buildingarea;
    /**
     * 总长度(米)
     */
    private BigDecimal buildinglength;
    /**
     * 总投资(万元)
     */
    private BigDecimal invest;
    /**
     * 工程造价(万元)
     */
    private BigDecimal engineering;
    /**
     * 项目规模
     */
    private String scale;
    /**
     * 开工日期
     */
    private Date startdate;
    /**
     * 竣工日期
     */
    private Date completeDate;
    /**
     * 经度
     */
    private BigDecimal lng;
    /**
     * 纬度
     */
    private BigDecimal lat;
    /**
     * 联系人姓名
     */
    private String linkman;
    /**
     * 联系人电话
     */
    private String linkphone;
    /**
     * 是否重点项目
     */
    private String ismajorProject;
    /**
     * 是否缴纳保证金
     */
    private String isdeposit;
    /**
     * 项目状态
     */
    private String prjstatus;
    /**
     * 机构ID
     */
    private Long deptId;
    /**
     * 住建局上报状态
     */
    private String stupstatus;
    /**
     * 标注位置（例如大门、项目部等）
     */
    private String markLocation;
    /**
     * 中心负责人id
     */
    private Long ps1201;
    /**
     * 电子围栏区域
     */
    private String region;
    /**
     * 是否上报
     */
    private String whetherReport;
    /**
     * 银行代码
     */
    private String bankCode;
    /**
     * 上报结果
     */
    private String upmsg;
}