package io.renren.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.dao.ZBankAccDao;
import io.renren.dto.ZBankAccDTO;
import io.renren.entity.ZBankAccEntity;
import io.renren.service.ZBankAccService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 泸州住建上报银行账户
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-04
 */
@Service
public class ZBankAccServiceImpl extends CrudServiceImpl<ZBankAccDao, ZBankAccEntity, ZBankAccDTO> implements ZBankAccService {

    @Override
    public QueryWrapper<ZBankAccEntity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<ZBankAccEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}