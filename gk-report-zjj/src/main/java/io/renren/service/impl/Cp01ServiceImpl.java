package io.renren.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.dao.Cp01Dao;
import io.renren.dto.Cp01DTO;
import io.renren.entity.Cp01Entity;
import io.renren.service.Cp01Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 企业基础信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-08-04
 */
@Service
public class Cp01ServiceImpl extends CrudServiceImpl<Cp01Dao, Cp01Entity, Cp01DTO> implements Cp01Service {

    @Override
    public QueryWrapper<Cp01Entity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Cp01Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}