package io.renren.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.dao.Ps06Dao;
import io.renren.dto.Ps06DTO;
import io.renren.entity.Ps06Entity;

import io.renren.service.Ps06Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 工人进退场信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-08-07
 */
@Service
public class Ps06ServiceImpl extends CrudServiceImpl<Ps06Dao, Ps06Entity, Ps06DTO> implements Ps06Service {

    @Override
    public QueryWrapper<Ps06Entity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Ps06Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}