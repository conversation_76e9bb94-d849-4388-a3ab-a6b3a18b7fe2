package io.renren.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.dao.ZDicConversionDao;
import io.renren.dto.ZDicConversionDTO;
import io.renren.entity.ZDicConversionEntity;
import io.renren.service.ZDicConversionService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 泸州住建上报字典转化
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-08-04
 */
@Service
public class ZDicConversionServiceImpl extends CrudServiceImpl<ZDicConversionDao, ZDicConversionEntity, ZDicConversionDTO> implements ZDicConversionService {

    @Override
    public QueryWrapper<ZDicConversionEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<ZDicConversionEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}