package io.renren.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.dao.Pj01Dao;
import io.renren.dto.Pj01DTO;
import io.renren.entity.Pj01Entity;
import io.renren.service.Pj01Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 项目基础信息表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-08-04
 */
@Service
public class Pj01ServiceImpl extends CrudServiceImpl<Pj01Dao, Pj01Entity, Pj01DTO> implements Pj01Service {

    @Override
    public QueryWrapper<Pj01Entity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Pj01Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}