package io.renren.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.dao.Kq02Dao;
import io.renren.dto.Kq02DTO;
import io.renren.entity.Kq02Entity;
import io.renren.service.Kq02Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 建筑工人考勤信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-08-06
 */
@Service
public class Kq02ServiceImpl extends CrudServiceImpl<Kq02Dao, Kq02Entity, Kq02DTO> implements Kq02Service {

    @Override
    public QueryWrapper<Kq02Entity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Kq02Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}