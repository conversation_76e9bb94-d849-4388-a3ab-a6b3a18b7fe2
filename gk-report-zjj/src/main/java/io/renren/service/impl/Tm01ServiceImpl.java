package io.renren.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.dao.Tm01Dao;
import io.renren.dto.Tm01DTO;
import io.renren.entity.Tm01Entity;
import io.renren.service.Tm01Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 班组基础信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-08-05
 */
@Service
public class Tm01ServiceImpl extends CrudServiceImpl<Tm01Dao, Tm01Entity, Tm01DTO> implements Tm01Service {

    @Override
    public QueryWrapper<Tm01Entity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Tm01Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}