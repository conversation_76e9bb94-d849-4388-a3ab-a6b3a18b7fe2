package io.renren.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.dao.Ps01Dao;
import io.renren.dto.Ps01DTO;
import io.renren.entity.Ps01Entity;
import io.renren.service.Ps01Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 人员实名基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-04
 */
@Service
public class Ps01ServiceImpl extends CrudServiceImpl<Ps01Dao, Ps01Entity, Ps01DTO> implements Ps01Service {

    @Override
    public QueryWrapper<Ps01Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<Ps01Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}