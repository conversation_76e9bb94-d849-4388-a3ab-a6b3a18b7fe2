package io.renren.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.dao.Ps04Dao;
import io.renren.dto.Ps04DTO;
import io.renren.entity.Ps04Entity;
import io.renren.service.Ps04Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 管理人员参建信息表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-08-06
 */
@Service
public class Ps04ServiceImpl extends CrudServiceImpl<Ps04Dao, Ps04Entity, Ps04DTO> implements Ps04Service {

    @Override
    public QueryWrapper<Ps04Entity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Ps04Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}