package io.renren.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.dao.Cp02Dao;
import io.renren.dto.Cp02DTO;
import io.renren.entity.Cp02Entity;
import io.renren.service.Cp02Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 参建单位信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-08-05
 */
@Service
public class Cp02ServiceImpl extends CrudServiceImpl<Cp02Dao, Cp02Entity, Cp02DTO> implements Cp02Service {

    @Override
    public QueryWrapper<Cp02Entity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Cp02Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}