package io.renren.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.dao.Ps02Dao;
import io.renren.dto.Ps02DTO;
import io.renren.entity.Ps02Entity;
import io.renren.service.Ps02Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 建筑工人信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-08-05
 */
@Service
public class Ps02ServiceImpl extends CrudServiceImpl<Ps02Dao, Ps02Entity, Ps02DTO> implements Ps02Service {

    @Override
    public QueryWrapper<Ps02Entity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Ps02Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}