package io.renren.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.renren.annotation.MordFilter;
import io.renren.common.ReportStatus;
import io.renren.common.SmzApiUrl;
import io.renren.common.exception.ExceptionUtils;
import io.renren.dao.*;
import io.renren.dto.*;
import io.renren.entity.*;
import io.renren.service.ReportService;
import io.renren.utils.SmzApiRequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022-03-30 15:57
 */

@Service
@Slf4j
public class ReportServiceImpl implements ReportService {
    /*@Autowired
    private AttendanceDao attendanceDao;*/
    @Autowired
    private SmzApiRequestUtil smzApiRequestUtil;
    @Autowired
    private SmzApiUrl smzApiUrl;
    /*@Autowired
    private UpLogUtils upLogUtils;
    @Autowired
    private WorkerDao workerDao;
    @Autowired
    private TeamDao teamDao;
    @Autowired
    private ManagerDao managerDao;
    @Autowired
    private PersonWalkExitDao personWalkExitDao;
    @Autowired
    private ProjectDao projectDao;
    @Autowired
    private CompanyDao companyDao;
    @Autowired
    private ConstructionDao constructionDao;
    @Autowired
    private BuilderLicenseService builderLicenseService;
    @Autowired
    private PersonExitDao personExitDao;
    @Autowired*/
    private Ps01Dao ps01Dao;
    @Autowired
    private Cp01Dao cp01Dao;
    @Autowired
    private Pj01Dao pj01Dao;
    @Autowired
    private Cp02Dao cp02Dao;
    @Autowired
    private Tm01Dao tm01Dao;
    @Autowired
    private Ps02Dao ps02Dao;
    @Autowired
    private Ps04Dao ps04Dao;
    @Autowired
    private Kq02Dao kq02Dao;
    @Autowired
    private Ps06Dao ps06Dao;
    @Autowired
    private Ps07Dao ps07Dao;
    /**
     * 成功状态
     */
    private static final String SUCCESS_CODE = "0";
    /**
     * 返回状态
     */
    private static final String RESPONSE_CODE = "code";
    /**
     * 成功状态
     */
    private static final String SUCCESS = "SUCCESS";

    /**
     * 工人 人员类型
     */
    private static final String WORKER_TYPE = "1";

    /**
     * 管理人员 人员类型
     */
    private static final String MANAGER_TYPE = "2";

    /**
     * 审核驳回
     */
    private static final String REJECTED_STATUS = "2";
    /**
     * 审核通过
     */
    private static final String PASS_STATUS = "1";

    @Override
    public void reportWorkerAttendance() {
        List<Kq02DTO> list = kq02Dao.getNeedUpWokerData();
        if (list.size() > 0) {
            Map<Long, List<Kq02DTO>> collectMap = list.stream().collect(Collectors.groupingBy(Kq02DTO::getPj0101));
            for (Long pj0101 : collectMap.keySet()) {
                sendData(collectMap.get(pj0101), smzApiUrl.getAttendanceAdd(), pj0101, collectMap.get(pj0101).get(0).getBankcode());
                log.info("开始上报项目【" + pj0101 + "】的工人考勤,总条数【" + collectMap.get(pj0101).size() + "】");
            }
        }
    }

    private void sendData(List<Kq02DTO> attendanceEntities, String url, Long pj0101, String bankcode) {
        try {
            Thread.sleep(3000);
            JSONArray jsonArray = JSON.parseArray(JSONObject.toJSONString(attendanceEntities, new MordFilter()));
            JSONObject result = smzApiRequestUtil.uploadData(bankcode, jsonArray, url);
            if (SUCCESS_CODE.equals(result.getString(RESPONSE_CODE))) {
                kq02Dao.batchUpdate(attendanceEntities);
            } else {
                log.error("项目【" + pj0101 + "】的考勤上报失败,接口返回结果" + result.toJSONString());
            }
        } catch (Exception e) {
            log.error("项目【" + pj0101 + "】的考勤上报出现异常:" + ExceptionUtils.getErrorStackTrace(e));
        }
    }

    @Override
    public void reportWorker() {
        List<Ps02DTO> list = ps02Dao.getNeedUpData();
        if (list.size() > 0) {
            Map<Long, List<Ps02DTO>> collectMap = list.stream().collect(Collectors.groupingBy(Ps02DTO::getPj0101));
            for (Long pj0101 : collectMap.keySet()) {
                //查询项目Code
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("projectcode", collectMap.get(pj0101).get(0).getProjectcode());
                jsonObject.put("ps02DTOS", JSONArray.parseArray(JSONObject.toJSONString(collectMap.get(pj0101), new MordFilter())));
                log.info("开始上报项目【" + pj0101 + "】的工人信息,总条数【" + collectMap.get(pj0101).size() + "】");
                sendWorker(jsonObject, smzApiUrl.getWorkerAdd(), pj0101, collectMap.get(pj0101).get(0).getBankcode());
            }
        }
    }

    private void sendWorker(JSONObject jsonObject, String url, Long pj0101, String bankcode) {
        try {
            Thread.sleep(2000);
            JSONObject result = smzApiRequestUtil.uploadData(bankcode, jsonObject, url);
            if (SUCCESS_CODE.equals(result.getString(RESPONSE_CODE))) {
                JSONArray data = result.getJSONArray("data");
                List<Ps02DTO> list = new ArrayList<>(data.size());
                for (int i = 0; i < data.size(); i++) {
                    JSONObject object = data.getJSONObject(i);
                    Ps02DTO ps02DTO = new Ps02DTO();
                    ps02DTO.setPj0101(pj0101);
                    ps02DTO.setIdcardnumber(object.getString("idcardNumber"));
                    String reportStatus = SUCCESS.equals(object.getString("ack_code")) ? ReportStatus.SUCCESS : ReportStatus.FAILURE;
                    ps02DTO.setStupstatus(reportStatus);
                    ps02DTO.setUpmsg(object.getString("msg"));
                    list.add(ps02DTO);
                }
                ps02Dao.batchUpdate(list);
            } else {
                log.error("上报项目【" + pj0101 + "】的工人信息失败,返回结果" + result.toJSONString());
            }
        } catch (Exception e) {
            log.error("项目【" + pj0101 + "】的工人上报,出现异常:" + ExceptionUtils.getErrorStackTrace(e));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reportTeam() {
        List<Tm01DTO> list = tm01Dao.getNeedUpData();
        if (list.size() > 0) {
            Map<Long, List<Tm01DTO>> collectMap = list.stream().collect(Collectors.groupingBy(Tm01DTO::getPj0101));
            for (Long pj0101 : collectMap.keySet()) {
                log.info("开始上报,项目【" + pj0101 + "】的班组信息,总条数【" + collectMap.get(pj0101).size() + "】");
                sendTeamData(collectMap.get(pj0101), smzApiUrl.getTeamAdd(), pj0101);
            }
        }
    }

    private void sendTeamData(List<Tm01DTO> teamEntities, String url, Long pj0101) {
        try {
            Thread.sleep(2000);
            //转换数据和上报
            JSONArray jsonArray = JSONArray.parseArray(JSONObject.toJSONString(teamEntities, new MordFilter()));
            JSONObject result = smzApiRequestUtil.uploadData(teamEntities.get(0).getBankcode(), jsonArray, url);
            //处理返回结果
            if (SUCCESS_CODE.equals(result.getString(RESPONSE_CODE))) {
                JSONArray data = result.getJSONArray("data");
                if (data.size() > 0) {
                    List<Tm01Entity> list = new ArrayList<>(data.size());
                    for (int i = 0; i < data.size(); i++) {
                        //获取返回数据
                        JSONObject jsonObject = data.getJSONObject(i);
                        Tm01Entity tm01Entity = new Tm01Entity();
                        tm01Entity.setPj0101(pj0101);
                        tm01Entity.setTeamname(jsonObject.getString("teamname"));
                        String reportStatus = SUCCESS.equals(jsonObject.getString("ack_code")) ? ReportStatus.SUCCESS : ReportStatus.FAILURE;
                        if (SUCCESS.equals(jsonObject.getString("ack_code"))) {
                            tm01Entity.setTeamsysno(jsonObject.getString("teamsysno"));
                        }
                        tm01Entity.setStupstatus(reportStatus);
                        tm01Entity.setUpmsg(jsonObject.getString("msg"));
                        list.add(tm01Entity);
                    }
                    tm01Dao.batchUpdate(list);
                }
            } else {
                log.error("上报项目【" + pj0101 + "】的班组信息失败,返回结果" + result.toJSONString());
            }
        } catch (Exception e) {
            log.error("项目【" + pj0101 + "】,班组信息上报出现异常:" + ExceptionUtils.getErrorStackTrace(e));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reportManager() {
        List<Ps04DTO> list = ps04Dao.getNeedUpData();
        if (list.size() > 0) {
            Map<Long, List<Ps04DTO>> collectMap = list.stream().collect(Collectors.groupingBy(Ps04DTO::getPj0101));
            for (Long pj0101 : collectMap.keySet()) {
                log.info("开始上报项目【" + pj0101 + "】的管理人员,总条数【" + collectMap.get(pj0101).size() + "】");
                sendManagerData(collectMap.get(pj0101), smzApiUrl.getManagerAdd(), pj0101, collectMap.get(pj0101).get(0).getBankcode());
            }
        }
    }

    private void sendManagerData(List<Ps04DTO> managerEntityList, String url, Long pj0101, String bankcode) {
        try {
            Thread.sleep(2000);
            JSONArray jsonArray = JSONArray.parseArray(JSONObject.toJSONString(managerEntityList, new MordFilter()));
            JSONObject result = smzApiRequestUtil.uploadData(bankcode, jsonArray, url);
            if (SUCCESS_CODE.equals(result.getString(RESPONSE_CODE))) {
                JSONArray data = result.getJSONArray("data");
                List<Ps04DTO> list = new ArrayList<>(data.size());
                for (int i = 0; i < data.size(); i++) {
                    JSONObject jsonObject = data.getJSONObject(i);
                    Ps04DTO ps04DTO = new Ps04DTO();
                    ps04DTO.setPj0101(pj0101);
                    ps04DTO.setIdcardnumber(jsonObject.getString("idcardNumber"));
                    ps04DTO.setCorpcode(jsonObject.getString("CorpCode"));
                    String reportStatus = SUCCESS.equals(jsonObject.getString("ack_code")) ? ReportStatus.SUCCESS : ReportStatus.FAILURE;
                    ps04DTO.setStupstatus(reportStatus);
                    ps04DTO.setUpmsg(jsonObject.getString("msg"));
                    list.add(ps04DTO);
                }
                ps04Dao.batchUpdate(list);
            } else {
                log.error("上报项目【" + pj0101 + "】的管理人员失败,返回结果" + result.toJSONString());
            }
        } catch (Exception e) {
            log.info("项目【" + pj0101 + "】管理人员上报出现异常:" + ExceptionUtils.getErrorStackTrace(e));
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reportWokerExit() {
        log.info("---开始上工人报退场信息---");
        List<Ps06DTO> list = ps06Dao.getNeedWokerExit();
        if (list.size() > 0) {
            Map<String, List<Ps06DTO>> collectMap = list.stream().collect(Collectors.groupingBy(Ps06DTO::getBankcode));
            for (String key : collectMap.keySet()) {
                List<Ps06DTO> ps06DTOS = collectMap.get(key);
                JSONArray jsonArray = JSONArray.parseArray(JSONObject.toJSONString(ps06DTOS, new MordFilter()));
                JSONObject result = smzApiRequestUtil.uploadData(key, jsonArray, smzApiUrl.getWorkerExit());
                JSONArray data = result.getJSONArray("data");
                Map<String, Ps06DTO> collect = ps06DTOS.stream().collect(Collectors.toMap(Ps06DTO::getIdcardnumber, Function.identity()));
                for (int i = 0; i < data.size(); i++) {
                    JSONObject jsonObject = data.getJSONObject(i);
                    String idcardnumber = jsonObject.getString("IdcardNumber");
                    Ps06DTO ps06DTO = collect.get(idcardnumber);
                    if (ps06DTO != null) {
                        Ps06Entity ps06Entity = new Ps06Entity();
                        ps06Entity.setPs0601(ps06DTO.getPs0601());
                        ps06Entity.setStupstatus(SUCCESS.equals(jsonObject.getString("ack_code")) ? ReportStatus.SUCCESS : ReportStatus.FAILURE);
                        ps06Entity.setUpmsg(jsonObject.getString("msg"));
                        ps06Dao.updateById(ps06Entity);
                    }
                }
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        log.info("---结束上报工人退场信息---");
    }

    @Override
    public void provincialCode() {
        /*//查询需要获取编码的数据,按照银行进行分组处理
        List<ProjectInfoDTO> dtoList = projectDao.selectProjectCode();
        Map<String, List<ProjectInfoDTO>> collectMap = dtoList.stream().collect(Collectors.groupingBy(ProjectInfoDTO::getBankcode));
        for (String bankCode : collectMap.keySet()) {
            try {
                log.info("开始获取项目省级编码,总条数【" + collectMap.get(bankCode).size() + "】");
                JSONArray jsonArray = JSONArray.parseArray(JSONObject.toJSONString(collectMap.get(bankCode), new MordFilter()));
                Thread.sleep(2000);
                //获取省级编码信息
                JSONObject result = smzApiRequestUtil.uploadBaseData(bankCode, jsonArray, smzApiUrl.getProvincialCode());
                if (SUCCESS_CODE.equals(result.getString(RESPONSE_CODE))) {
                    JSONArray data = result.getJSONArray("data");
                    for (int i = 0; i < data.size(); i++) {
                        JSONObject jsonObject = data.getJSONObject(i);
                        //市级编码
                        String projectCode = jsonObject.getString("ProjectCode");
                        //省级编码
                        String provinceCode = jsonObject.getString("ProvinceCode");
                        //更新数据库省级编码
                        projectDao.updateProvinceCode(projectCode, provinceCode);
                    }
                } else {
                    log.error("项目省级编码获取失败,接口返回数据:" + result.toJSONString());
                }
            } catch (Exception e) {
                log.error("项目省级编码获取出现异常:" + ExceptionUtils.getErrorStackTrace(e));
            }

        }*/

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void partApprovalStatus() {
        List<Cp02DTO> construction = cp02Dao.getNeedStatus();
        //查询待审核的数据获取审核结果
        for (Cp02DTO constructionEntity : construction) {
            log.info("开始获取参建单位【" + constructionEntity.getCp0201() + "】审核状态");
            try {
                Thread.sleep(2000);
                JSONObject jsonObject = JSON.parseObject(JSONObject.toJSONString(constructionEntity, new MordFilter()));
                JSONObject result = smzApiRequestUtil.uploadBaseData(constructionEntity.getBankcode(), jsonObject, smzApiUrl.partApprovalStatus());
                //审核状态
                String auditStatus = result.getJSONArray("data").getJSONObject(0).getString("auditStatus");
                Cp02Entity cp02Entity = new Cp02Entity();
                cp02Entity.setAuditstatus(auditStatus);
                cp02Entity.setAuditmsg(result.getJSONArray("data").getJSONObject(0).getString("msg"));
                cp02Entity.setUpdateDate(new Date());
                cp02Entity.setCp0201(constructionEntity.getCp0201());
                cp02Dao.updateById(cp02Entity);
            } catch (Exception e) {
                log.error("获取参建单位【" + constructionEntity.getCp0201() + "审核状态出现异常:" + ExceptionUtils.getErrorStackTrace(e));
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void managerApprovalStatus() {
        List<Ps04DTO> list = ps04Dao.getNeedStatus();
        Map<Long, List<Ps04DTO>> collectMap = list.stream().collect(Collectors.groupingBy(Ps04DTO::getPj0101));
        //数据上报
        for (Long pj0101 : collectMap.keySet()) {
            List<Ps04DTO> ps04DTOS = collectMap.get(pj0101);
            log.info("开始获取项目【" + pj0101 + "】的管理人员审核状态,总条数【" + ps04DTOS.size() + "】");
            try {
                Thread.sleep(2000);
                JSONArray jsonArray = JSONArray.parseArray(JSONObject.toJSONString(ps04DTOS, new MordFilter()));
                //获取管理人员审核状态
                JSONObject result = smzApiRequestUtil.uploadData(ps04DTOS.get(0).getBankcode(), jsonArray, smzApiUrl.managerApprovalStatus());
                if (SUCCESS_CODE.equals(result.getString(RESPONSE_CODE))) {
                    JSONArray data = result.getJSONArray("data");
                    Map<String, Ps04DTO> collect = ps04DTOS.stream().collect(Collectors.toMap(Ps04DTO::getIdcardnumber, Function.identity()));
                    for (int i = 0; i < data.size(); i++) {
                        JSONObject jsonObject = data.getJSONObject(i);
                        //身份证号码
                        String idCardNumber = jsonObject.getString("idcardNumber");
                        Ps04DTO ps04DTO = collect.get(idCardNumber);
                        if (ps04DTO != null) {
                            ps04DTO.setAuditstatus(jsonObject.getString("auditStatus"));
                            ps04DTO.setAuditmsg(jsonObject.getString("msg"));
                            Ps04Entity ps04Entity = BeanUtil.copyProperties(ps04DTO, Ps04Entity.class);
                            ps04Entity.setUpdateDate(new Date());
                            ps04Dao.updateById(ps04Entity);
                        }
                    }
                } else {
                    log.error("获取项目【" + pj0101 + "】管理员审核状态失败,接口返回:" + result.toJSONString());
                }
            } catch (Exception e) {
                log.error("获取项目【" + pj0101 + "】的管理人员审核状态,出现异常:" + ExceptionUtils.getErrorStackTrace(e));
            }

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reportCompany() {
        Cp01DTO cp01DTO = cp01Dao.getNeedUpData();
        if (cp01DTO != null) {
            try {
                log.info("开始上报企业ID【" + cp01DTO.getCp0101() + "】的基础信息");
                JSONObject jsonObject = JSON.parseObject(JSONObject.toJSONString(cp01DTO, new MordFilter()));
                //上报数据
                JSONObject result = smzApiRequestUtil.uploadBaseData(cp01DTO.getBankCode(), jsonObject, smzApiUrl.getEnterpriseAdd());
                String reportStatus = SUCCESS_CODE.equals(result.getString(RESPONSE_CODE)) ? ReportStatus.SUCCESS : ReportStatus.FAILURE;
                Cp01Entity cp01Entity = BeanUtil.copyProperties(cp01DTO, Cp01Entity.class);
                cp01Entity.setStupstatus(reportStatus);
                cp01Entity.setUpmsg(result.getString("data"));
                cp01Entity.setUpdateDate(new Date());
                cp01Dao.updateById(cp01Entity);
            } catch (Exception e) {
                log.error("企业【" + cp01DTO.getCp0101() + "】基础信息上报,出现异常:" + ExceptionUtils.getErrorStackTrace(e));
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reportConstruction() {
        Cp02DTO cp02DTO = cp02Dao.getNeedUpData();
        if (cp02DTO != null) {
            try {
                JSONObject jsonObject = JSON.parseObject(JSONObject.toJSONString(cp02DTO, new MordFilter()));
                //上报数据
                log.info("开始上报参建单位【" + cp02DTO.getCp0201() + "】信息");
                JSONObject result = smzApiRequestUtil.uploadBaseData(cp02DTO.getBankcode(), jsonObject, smzApiUrl.getCompanyAdd());
                String reportStatus = SUCCESS_CODE.equals(result.getString(RESPONSE_CODE)) ? ReportStatus.SUCCESS : ReportStatus.FAILURE;
                Cp02Entity cp02Entity = new Cp02Entity();
                cp02Entity.setCp0201(cp02DTO.getCp0201());
                cp02Entity.setStupstatus(reportStatus);
                cp02Entity.setUpmsg(result.getString("data"));
                cp02Entity.setUpdateDate(new Date());
                cp02Dao.updateById(cp02Entity);
            } catch (Exception e) {
                log.error("参建单位【" + cp02DTO.getCp0201() + "】,基础信息上报出现异常:" + ExceptionUtils.getErrorStackTrace(e));
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reportPerson() {
        List<Ps01DTO> ps01Entities = ps01Dao.getNeedUpData();
        if (ps01Entities.size() > 0) {
            try {
                JSONArray jsonArray = JSONArray.parseArray(JSONObject.toJSONString(ps01Entities, new MordFilter()));
                log.info("上报人员基础信息 {} 条", ps01Entities.size());
                JSONObject result = smzApiRequestUtil.uploadBaseData(ps01Entities.get(ps01Entities.size() - 1).getBankCode(), jsonArray, smzApiUrl.getPersonnelAdd());
                if (SUCCESS_CODE.equals(result.getString(RESPONSE_CODE))) {
                    JSONArray data = result.getJSONArray("data");
                    List<Ps01DTO> list = new ArrayList<>(data.size());
                    for (int i = 0; i < data.size(); i++) {
                        JSONObject object = data.getJSONObject(i);
                        Ps01DTO ps01DTO = new Ps01DTO();
                        ps01DTO.setIdcardnumber(object.getString("idcardNumber"));
                        ps01DTO.setName(object.getString("name"));
                        ps01DTO.setStupstatus(SUCCESS.equals(object.getString("ack_code")) ? ReportStatus.SUCCESS : ReportStatus.FAILURE);
                        ps01DTO.setUpmsg(object.getString("msg"));
                        list.add(ps01DTO);
                    }
                    ps01Dao.batchUpdate(list);
                } else {
                    log.error("人员基础上报失败,接口返回结果:" + result.toJSONString());
                }
            } catch (Exception e) {
                log.error("人员基础信息上报,出现异常:" + ExceptionUtils.getErrorStackTrace(e));
            }

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reportProject() {
        Pj01DTO pj01DTO = pj01Dao.getNeedUpData();
        if (pj01DTO != null) {
            JSONObject jsonObject = JSON.parseObject(JSONObject.toJSONString(pj01DTO, new MordFilter()));
            log.info("开始上报项目【" + pj01DTO.getPj0101() + "】");
            try {
                JSONObject result = smzApiRequestUtil.uploadData(pj01DTO.getBankcode(), jsonObject, smzApiUrl.getProjectAdd());
                String reportStatus = SUCCESS_CODE.equals(result.getString("code")) ? ReportStatus.SUCCESS : ReportStatus.FAILURE;
                Pj01Entity pj01Entity = new Pj01Entity();
                pj01Entity.setPj0101(pj01DTO.getPj0101());
                //如果结果是成功
                if (SUCCESS_CODE.equals(result.getString(RESPONSE_CODE))) {
                    //项目编码
                    pj01Entity.setCode(result.getJSONObject("data").getString("projectcode"));
                }
                pj01Entity.setStupstatus(reportStatus);
                pj01Entity.setUpmsg(result.getString("msg"));
                pj01Dao.updateById(pj01Entity);
            } catch (Exception e) {
                log.error("项目【" + pj01DTO.getPj0101() + "】上报出现异常:" + ExceptionUtils.getErrorStackTrace(e));
            }
        }
    }

    @Override
    public void reportBuilderLicense() {
        /*BuilderLicenseEntity builderLicenseEntity = builderLicenseService.getOne(new QueryWrapper<BuilderLicenseEntity>().eq(Constant.REPORTSTATUS, "0").isNotNull(Constant.PROJECTCODE).eq("rownum", "1"));
        if (BeanUtil.isNotEmpty(builderLicenseEntity)) {
            JSONObject jsonObject = JSON.parseObject(JSONObject.toJSONString(builderLicenseEntity, new MordFilter()));
            JSONObject result = smzApiRequestUtil.uploadData(builderLicenseEntity.getPj0101(), jsonObject, smzApiUrl.getBuilderlicenseAdd());
            log.info(result.getString("msg"));
            builderLicenseEntity.setReportstatus(ReportStatus.SUCCESS);
            builderLicenseService.saveOrUpdate(builderLicenseEntity);
        }*/
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reportManagerAttendance() {
        List<Kq02DTO> list = kq02Dao.getNeedUpManagerData();
        if (list.size() > 0) {
            Map<Long, List<Kq02DTO>> listMap = list.stream().collect(Collectors.groupingBy(Kq02DTO::getPj0101));
            for (Long pj0101 : listMap.keySet()) {
                sendData(listMap.get(pj0101), smzApiUrl.getAddAttendance(), pj0101, listMap.get(pj0101).get(0).getBankcode());
                log.info("上报项目【" + pj0101 + "】的管理人员考勤,总条数【" + listMap.get(pj0101).size() + "】");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reportManagerExit() {
        log.info("---开始上管理人员报退场信息---");
        List<Ps07DTO> list = ps07Dao.getNeedManagerExit();
        if (list.size() > 0) {
            Map<String, List<Ps07DTO>> collectMap = list.stream().collect(Collectors.groupingBy(Ps07DTO::getBankcode));
            for (String key : collectMap.keySet()) {
                List<Ps07DTO> ps07DTOS = collectMap.get(key);
                JSONArray jsonArray = JSONArray.parseArray(JSONObject.toJSONString(ps07DTOS, new MordFilter()));
                JSONObject result = smzApiRequestUtil.uploadData(key, jsonArray, smzApiUrl.getManagerExit());
                JSONArray data = result.getJSONArray("data");
                Map<String, Ps07DTO> collect = ps07DTOS.stream().collect(Collectors.toMap(Ps07DTO::getIdcardnumber, Function.identity()));
                for (int i = 0; i < data.size(); i++) {
                    JSONObject jsonObject = data.getJSONObject(i);
                    String idcardnumber = jsonObject.getString("idcardnumber");
                    Ps07DTO ps07DTO = collect.get(idcardnumber);
                    if (ps07DTO != null) {
                        Ps07Entity ps07Entity = new Ps07Entity();
                        ps07Entity.setPs0701(ps07DTO.getPs0701());
                        ps07Entity.setStupstatus(SUCCESS.equals(jsonObject.getString("ack_code")) ? ReportStatus.SUCCESS : ReportStatus.FAILURE);
                        ps07Entity.setUpmsg(jsonObject.getString("msg"));
                        ps07Dao.updateById(ps07Entity);
                    }
                }
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        log.info("---结束上报管理人员退场信息---");
    }

    /*@SneakyThrows
    private void sendPersonExitData(List<PersonWalkExitDTO> personWalkExitDTOList, String persontype, Long pj0101) {
        JSONArray jsonArray = JSONArray.parseArray(JSONObject.toJSONString(personWalkExitDTOList, new MordFilter()));
        JSONObject result = null;
        if (WORKER_TYPE.equals(persontype)) {
            result = smzApiRequestUtil.uploadData(pj0101, jsonArray, smzApiUrl.getWorkerExit());
        } else {
            result = smzApiRequestUtil.uploadData(pj0101, jsonArray, smzApiUrl.getManagerExit());
        }
        JSONArray data = result.getJSONArray("data");
        for (int i = 0; i < data.size(); i++) {
            JSONObject jsonObject = data.getJSONObject(i);
            List<PersonWalkExitEntity> personWalkExitEntities = personWalkExitDao.selectList(new QueryWrapper<PersonWalkExitEntity>()
                    .eq(Constant.PROJECTCODE, jsonObject.getString("ProjectCode"))
                    .eq("idcardnumber", jsonObject.getString("IdcardNumber")));
            for (PersonWalkExitEntity personWalkExitEntity : personWalkExitEntities) {
                String reportStatus = personWalkExitEntity.getReportstatus();
                if (!ReportStatus.SUCCESS.equals(reportStatus)) {
                    if (SUCCESS.equals(jsonObject.getString("ack_code"))) {
                        reportStatus = ReportStatus.SUCCESS;
                    } else {
                        reportStatus = ReportStatus.FAILURE;
                    }
//                    方法暂时无效
//                    upLogUtils.saveLog(jsonObject.getString("msg"), "personwalkexit", personWalkExitEntity.getId());
                }
                personWalkExitEntity.setReportstatus(reportStatus);
                personWalkExitDao.updateById(personWalkExitEntity);
            }
        }
    }*/

    /*@SneakyThrows
    private void sendPersonWalkExitData(List<PersonWalkExitEntity> personExitDTOList, String persontype, Long pj0101) {
        JSONArray jsonArray = JSONArray.parseArray(JSONObject.toJSONString(personExitDTOList, new MordFilter()));
        JSONObject result = null;
        if (WORKER_TYPE.equals(persontype)) {
            result = smzApiRequestUtil.uploadData(pj0101, jsonArray, smzApiUrl.getWorkerExit());
        } else {
            result = smzApiRequestUtil.uploadData(pj0101, jsonArray, smzApiUrl.getManagerExit());
        }
        JSONArray data = result.getJSONArray("data");
        for (int i = 0; i < data.size(); i++) {
            JSONObject jsonObject = data.getJSONObject(i);
            String idcardnumber = StringUtils.isBlank(jsonObject.getString("idcardnumber"))
                    ? jsonObject.getString("IdcardNumber") : jsonObject.getString("idcardnumber");
            List<PersonWalkExitEntity> personWalkExitEntities = personWalkExitDao.selectList(new QueryWrapper<PersonWalkExitEntity>()
                    .eq(Constant.PROJECTCODE, jsonObject.getString("ProjectCode"))
                    .eq("idcardnumber", idcardnumber));
            for (PersonWalkExitEntity personExitEntity : personWalkExitEntities) {
                String reportStatus = personExitEntity.getReportstatus();
                if (!ReportStatus.SUCCESS.equals(reportStatus)) {
                    if (SUCCESS.equals(jsonObject.getString("ack_code"))) {
                        reportStatus = ReportStatus.SUCCESS;
                        //退场成功之后更新退场时间
                        workerDao.updateExitTime(personExitEntity.getExittime(),
                                personExitEntity.getIdcardnumber(),
                                personExitEntity.getProjectcode(), persontype);
                    } else {
                        reportStatus = ReportStatus.FAILURE;
                        personExitEntity.setErrorMessage(jsonObject.getString("msg"));
                    }
                }
                personExitEntity.setReportstatus(reportStatus);
                personWalkExitDao.update(personExitEntity, new UpdateWrapper<PersonWalkExitEntity>()
                        .eq("IdcardNumber", personExitEntity.getIdcardnumber())
                        .eq("ProjectCode", personExitEntity.getProjectcode())
                );
            }
        }
        Thread.sleep(2000);
    }*/

    /*private void sendPersonTestExitData(List<PersonExitEntity> personExitDTOList, String persontype, Long pj0101) {
        JSONArray jsonArray = JSONArray.parseArray(JSONObject.toJSONString(personExitDTOList, new MordFilter()));
        JSONObject result = null;
        if (WORKER_TYPE.equals(persontype)) {
            result = smzApiRequestUtil.uploadData(pj0101, jsonArray, smzApiUrl.getWorkerExit());
        } else {
            result = smzApiRequestUtil.uploadData(pj0101, jsonArray, smzApiUrl.getManagerExit());
        }
        JSONArray data = result.getJSONArray("data");
        System.out.println(data.toJSONString());
        for (int i = 0; i < data.size(); i++) {
            JSONObject jsonObject = data.getJSONObject(i);
            List<PersonExitEntity> personWalkExitEntities = personExitDao.selectList(new QueryWrapper<PersonExitEntity>()
                    .eq(Constant.PROJECTCODE, jsonObject.getString("ProjectCode"))
                    .eq("idcardnumber", jsonObject.getString("idcardnumber")));
            for (PersonExitEntity personExitEntity : personWalkExitEntities) {
                String reportStatus = personExitEntity.getReportstatus();
                if (!ReportStatus.SUCCESS.equals(reportStatus)) {
                    if (SUCCESS.equals(jsonObject.getString("ack_code"))) {
                        reportStatus = ReportStatus.SUCCESS;
                    } else {
                        reportStatus = ReportStatus.FAILURE;
                        personExitEntity.setErrorMessage(jsonObject.getString("msg"));
                    }
                }
                personExitEntity.setReportstatus(reportStatus);
                personExitDao.updateById(personExitEntity);
            }
        }
    }*/
}
