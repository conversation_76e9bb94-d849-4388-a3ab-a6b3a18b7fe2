package io.renren.service;

/**
 * <AUTHOR>
 * @Date 2022-03-30 15:57
 */


public interface ReportService {
    /**
     * 上报考勤记录
     */
    void reportWorkerAttendance();

    /**
     * 上报工人信息
     */
    void reportWorker();

    /**
     * 上报班组信息
     */
    void reportTeam();

    /**
     * 上报管理人员信息
     */
    void reportManager();

    /**
     * 上报退场人员数据
     */
    void reportWokerExit();

    /**
     * 获取项目省级编码
     */
    void provincialCode();

    /**
     * 获取参建单位审核状态
     */
    void partApprovalStatus();

    /**
     * 获取管理人员审核状态
     */
    void managerApprovalStatus();

    /**
     * 企业基础信息
     */
    void reportCompany();

    /**
     * 参建单位信息
     */
    void reportConstruction();

    /**
     * 人员基础信息
     */
    void reportPerson();

    /**
     * 项目信息
     */
    void reportProject();

    /**
     * 施工许可证
     */
    void reportBuilderLicense();

    /**
     * 管理人员考勤
     */
    void reportManagerAttendance();

    void reportManagerExit();
}
