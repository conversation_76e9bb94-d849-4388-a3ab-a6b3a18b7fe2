package io.renren.common;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 *  @title SmzApiUrl
 *  @Description 住建局实名制上报接口地址
 *  <AUTHOR>
 *  @Date 2020/4/10 15:21
 *  @Copyright 2019-2025
 */
@Component
@Data
public class SmzApiUrl {
    @Value("${system.configure.smzApi.apiRoot}")
    private String apiRoot;
    public String getLogin(){return apiRoot+"api/login";}
    public String getProjectAdd(){return apiRoot+"api/project/add/v1";}
    public String getProjectUpdate(){
        return apiRoot+"api/project/update/v1";
    }
    public String getEnterpriseAdd(){
        return apiRoot+"api/enterprise/add/v1";
    }
    public String getEnterpriseUpdate(){
        return apiRoot+"api/enterprise/update/v1";
    }
    public String getCompanyAdd(){
        return apiRoot+"api/company/add/v1";
    }
    public String getCompanyUpdate(){
        return apiRoot+"api/company/update/v1";
    }
    public String getPersonnelAdd(){
        return apiRoot+"api/personnel/add/v1";
    }
    public String getProvincialCode(){
        return apiRoot+"api/project/get/v1";
    }
    public String partApprovalStatus(){
        return apiRoot+"api/company/query/v1";
    }
    public String managerApprovalStatus(){
        return apiRoot+"api/manager/query/v1";
    }
    public String getPersonnelUpdate(){
        return apiRoot+"api/personnel/update/v1";
    }
    public String getWorkerAdd(){
        return apiRoot+"api/worker/add/v1";
    }
    public String getWorkerExit(){
        return apiRoot+"api/worker/exit/v1";
    }
    public String getWorkerUpdate(){
        return apiRoot+"api/worker/update/v1";
    }
    public String getTeamAdd(){
        return apiRoot+"api/team/add/v1";
    }
    public String getTeamUpdate(){
        return apiRoot+"api/team/update/v1";
    }
    public String getManagerAdd(){
        return apiRoot+"api/manager/add/v1";
    }
    public String getManagerExit(){
        return apiRoot+"api/manager/exit/v1";
    }
    public String getManagerUpdate(){
        return apiRoot+"api/manager/update/v1";
    }
    public String getAttendanceAdd(){
        return apiRoot+"api/attendance/add/v1";
    }
    public String getBuilderlicenseAdd(){
        return apiRoot+"api/builderlicense/add/v1";
    }
    public String getAddAttendance(){
        return apiRoot+"api/attendance/addManager/v1";
    }
    public String getSpecialAdd(){
        return apiRoot+"api/special/add/v1";
    }
    public String getWorkersalaryAdd(){
        return apiRoot+"api/workersalary/add/v1";
    }
    public String getDeviceinfoAdd(){
        return apiRoot+"api/deviceinfo/add/v1";
    }
    public String getDeviceinfoUpdate(){
        return apiRoot+"api/deviceinfo/update/v1";
    }
    public String getDeviceuseAdd(){
        return apiRoot+"api/deviceuse/add/v1";
    }
    public String getOiluseinfoAdd(){
        return apiRoot+"api/oiluseinfo/add/v1";
    }
}
