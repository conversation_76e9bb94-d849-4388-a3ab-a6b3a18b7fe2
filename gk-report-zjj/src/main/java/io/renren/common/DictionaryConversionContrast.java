package io.renren.common;

import io.renren.dao.ZDicConversionDao;
import io.renren.dto.ZDicConversionDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *  @title InitAreaCodeContrast
 *  @Description 字典表转换
 *  <AUTHOR>
 *  @Date 2020/4/13 16:00
 *  @Copyright 2019-2025
 */
@Component
public class DictionaryConversionContrast implements ApplicationRunner {

    private Logger logger = LoggerFactory.getLogger(DictionaryConversionContrast.class);

    public static Map<String,Map<String,String>> dictionaryConversionMap = new HashMap<>();

    @Autowired
    private ZDicConversionDao zDicConversionDao;

    @Override
    public void run(ApplicationArguments args){
        logger.info("初始化字典转换数据");
        //查询需要转换的类型
        List<String> list = zDicConversionDao.selectConversionType();
        for (String type:list) {
            Map<String, ZDicConversionDTO> map = zDicConversionDao.selectByTypeToMap(type);
            Map<String, String> hashMap = new HashMap<>();
            for (String key:map.keySet()) {
                hashMap.put(key,map.get(key).getConversionValue());
            }
            dictionaryConversionMap.put(type,hashMap);
        }
    }
}
