//package io.renren.config;
//
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.scheduling.annotation.EnableAsync;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//
//import java.util.concurrent.ThreadPoolExecutor;
//
///**
// * <AUTHOR>
// * @Date 2021-02-02 14:26
// */
//@Configuration
//@EnableAsync
//public class PoolConfig {
//    @Bean(name = "reportAttendance")
//    public ThreadPoolTaskExecutor attendance() {
//        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
//        // 设置核心线程数
//        executor.setCorePoolSize(3);
//        // 设置最大线程数
//        executor.setMaxPoolSize(6);
//        // 设置队列容量
//        executor.setQueueCapacity(10000);
//        // 设置线程活跃时间（秒）
//        executor.setKeepAliveSeconds(60);
//        // 设置默认线程名称
//        executor.setThreadNamePrefix("attendance-");
//        // 设置拒绝策略
//        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
//        // 等待所有任务结束后再关闭线程池
//        executor.setWaitForTasksToCompleteOnShutdown(true);
//        return executor;
//    }
//
//    @Bean(name = "reportWorker")
//    public ThreadPoolTaskExecutor reportWorker() {
//        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
//        // 设置核心线程数
//        executor.setCorePoolSize(4);
//        // 设置最大线程数
//        executor.setMaxPoolSize(8);
//        // 设置队列容量
//        executor.setQueueCapacity(10000);
//        // 设置线程活跃时间（秒）
//        executor.setKeepAliveSeconds(60);
//        // 设置默认线程名称
//        executor.setThreadNamePrefix("worker-");
//        // 设置拒绝策略
//        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
//        // 等待所有任务结束后再关闭线程池
//        executor.setWaitForTasksToCompleteOnShutdown(true);
//        return executor;
//    }
//
//    @Bean(name = "reportTeam")
//    public ThreadPoolTaskExecutor reportTeam() {
//        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
//        // 设置核心线程数
//        executor.setCorePoolSize(4);
//        // 设置最大线程数
//        executor.setMaxPoolSize(8);
//        // 设置队列容量
//        executor.setQueueCapacity(10000);
//        // 设置线程活跃时间（秒）
//        executor.setKeepAliveSeconds(60);
//        // 设置默认线程名称
//        executor.setThreadNamePrefix("team-");
//        // 设置拒绝策略
//        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
//        // 等待所有任务结束后再关闭线程池
//        executor.setWaitForTasksToCompleteOnShutdown(true);
//        return executor;
//    }
//
//    @Bean(name = "provincialCode")
//    public ThreadPoolTaskExecutor provincialCode() {
//        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
//        // 设置核心线程数
//        executor.setCorePoolSize(4);
//        // 设置最大线程数
//        executor.setMaxPoolSize(8);
//        // 设置队列容量
//        executor.setQueueCapacity(10000);
//        // 设置线程活跃时间（秒）
//        executor.setKeepAliveSeconds(60);
//        // 设置默认线程名称
//        executor.setThreadNamePrefix("provincialCode-");
//        // 设置拒绝策略
//        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
//        // 等待所有任务结束后再关闭线程池
//        executor.setWaitForTasksToCompleteOnShutdown(true);
//        return executor;
//    }
//    @Bean(name = "enterprise")
//    public ThreadPoolTaskExecutor enterprise() {
//        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
//        // 设置核心线程数
//        executor.setCorePoolSize(4);
//        // 设置最大线程数
//        executor.setMaxPoolSize(8);
//        // 设置队列容量
//        executor.setQueueCapacity(10000);
//        // 设置线程活跃时间（秒）
//        executor.setKeepAliveSeconds(60);
//        // 设置默认线程名称
//        executor.setThreadNamePrefix("enterprise-");
//        // 设置拒绝策略
//        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
//        // 等待所有任务结束后再关闭线程池
//        executor.setWaitForTasksToCompleteOnShutdown(true);
//        return executor;
//    }
//    @Bean(name = "projectInfo")
//    public ThreadPoolTaskExecutor projectInfo() {
//        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
//        // 设置核心线程数
//        executor.setCorePoolSize(4);
//        // 设置最大线程数
//        executor.setMaxPoolSize(8);
//        // 设置队列容量
//        executor.setQueueCapacity(10000);
//        // 设置线程活跃时间（秒）
//        executor.setKeepAliveSeconds(60);
//        // 设置默认线程名称
//        executor.setThreadNamePrefix("projectInfo-");
//        // 设置拒绝策略
//        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
//        // 等待所有任务结束后再关闭线程池
//        executor.setWaitForTasksToCompleteOnShutdown(true);
//        return executor;
//    }
//}
