package io.renren.modules.enterprise.kq04.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.kq04.dto.Kq04DTO;
import io.renren.modules.enterprise.kq04.service.Kq04Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * 考勤设备指令表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-07-20
 */
@RestController
@RequestMapping("enterprise/kq04")
@Api(tags = "考勤设备指令表")
public class Kq04Controller {
    @Autowired
    private Kq04Service kq04Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("enterprise:kq04:page")
    public Result<PageData<Kq04DTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Kq04DTO> page = kq04Service.page(params);

        return new Result<PageData<Kq04DTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:kq04:info")
    public Result<Kq04DTO> get(@PathVariable("id") Long id) {
        Kq04DTO data = kq04Service.get(id);

        return new Result<Kq04DTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("enterprise:kq04:save")
    public Result save(@RequestBody Kq04DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        kq04Service.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("enterprise:kq04:update")
    public Result update(@RequestBody Kq04DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        kq04Service.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("enterprise:kq04:delete")
    public Result delete(@RequestBody Long[] ids) {
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        kq04Service.delete(ids);

        return new Result();
    }

}