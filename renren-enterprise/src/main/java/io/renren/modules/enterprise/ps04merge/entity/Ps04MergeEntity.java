package io.renren.modules.enterprise.ps04merge.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.renren.common.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 管理人员合并表
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-01-04
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_PS04_MERGE")
public class Ps04MergeEntity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
	@TableId(type = IdType.ASSIGN_ID)
	private Long id;
    /**
     * 管理人员ID集合（,分隔）
     */
	private String ps0401s;
    /**
     * 项目ID集合（,分隔）
     */
	private String pj0101s;
	/**
	 * 申请项目id
	 */
	private Long pj0101;
    /**
     * 申请企业ID
     */
	private Long cp0101;
    /**
     * 备注
     */
	private String memo;
    /**
     * 审核状态(0待审核，1通过，2不通过)
     */
	private String auditstatus;
    /**
     * 审核人
     */
	private String auditor;
    /**
     * 审核时间
     */
	private Date auditdate;
    /**
     * 审核结果
     */
	private String auditresult;
	/**
     * 创建者
     */
	@TableField(fill = FieldFill.INSERT)
	private Long  creator;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;
}