<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.regul.cd02.dao.Cd02Dao">

    <select id="getList" resultType="io.renren.modules.regul.cd02.dto.Cd02DTO">
        select a.credittype,
                b.corpname as name,
                b.corpcode as uniquecode,
                (select x.name from b_pj01 x where x.pj0101 = t.pj0101) as projectname,
                t.*
          from B_CD02 t, b_cd01 a, b_cp01 b
         where t.cd0101 = a.cd0101
           and a.creditno = b.cp0101
           and a.credittype = '1'
           and t.rewardstatus > 0
        <if test="cd0101 != null and cd0101 != ''">
            and t.cd0101 = #{cd0101}
        </if>
        <if test="name != null and name != ''">
            and b.corpname like '%' || #{name} || '%'
        </if>
        <if test="credittype != null and credittype != ''">
            and a.credittype = #{credittype}
        </if>
        union all
        select a.credittype,
                b.teamname as name,
                b.teamsysno as uniquecode,
                (select x.name from b_pj01 x where x.pj0101 = t.pj0101) as projectname,
                t.*
          from B_CD02 t, b_cd01 a, b_tm01 b
         where t.cd0101 = a.cd0101
           and a.creditno = b.tm0101
           and a.credittype = '2'
           and t.rewardstatus > 0
        <if test="cd0101 != null and cd0101 != ''">
            and t.cd0101 = #{cd0101}
        </if>
        <if test="name != null and name != ''">
            and b.teamname like '%' || #{name} || '%'
        </if>
        <if test="credittype != null and credittype != ''">
            and a.credittype = #{credittype}
        </if>
        union all
        select a.credittype,
                b.name as name,
                b.idcardnumber as uniquecode,
                (select x.name from b_pj01 x where x.pj0101 = t.pj0101) as projectname,
                t.*
          from B_CD02 t, b_cd01 a, b_ps01 b
         where t.cd0101 = a.cd0101
           and a.creditno = b.ps0101
           and a.credittype = '3'
           and t.rewardstatus > 0
        <if test="cd0101 != null and cd0101 != ''">
            and t.cd0101 = #{cd0101}
        </if>
        <if test="name != null and name != ''">
            and b.name like '%' || #{name} || '%'
        </if>
        <if test="credittype != null and credittype != ''">
            and a.credittype = #{credittype}
        </if>
    </select>
    <select id="publicizepage" resultType="io.renren.modules.regul.cd02.dto.Cd02DTO">
        select a.credittype,
        b.corpname as name,
        b.corpcode as uniquecode,
        (select x.name from b_pj01 x where x.pj0101 = t.pj0101) as projectname,
        t.*
        from B_CD02 t, b_cd01 a, b_cp01 b
        where t.cd0101 = a.cd0101
        and a.creditno = b.cp0101
        and a.credittype = '1'
        and t.rewardstatus = '3'
        <if test="name != null and name != ''">
            and b.corpname like '%' || #{name} || '%'
        </if>
        <if test="credittype != null and credittype != ''">
            and a.credittype = #{credittype}
        </if>
        union all
        select a.credittype,
        b.teamname as name,
        b.teamsysno as uniquecode,
        (select x.name from b_pj01 x where x.pj0101 = t.pj0101) as projectname,
        t.*
        from B_CD02 t, b_cd01 a, b_tm01 b
        where t.cd0101 = a.cd0101
        and a.creditno = b.tm0101
        and a.credittype = '2'
        and t.rewardstatus = '3'
        <if test="name != null and name != ''">
            and b.teamname like '%' || #{name} || '%'
        </if>
        <if test="credittype != null and credittype != ''">
            and a.credittype = #{credittype}
        </if>
        union all
        select a.credittype,
        b.name as name,
        b.idcardnumber as uniquecode,
        (select x.name from b_pj01 x where x.pj0101 = t.pj0101) as projectname,
        t.*
        from B_CD02 t, b_cd01 a, b_ps01 b
        where t.cd0101 = a.cd0101
        and a.creditno = b.ps0101
        and a.credittype = '3'
        and t.rewardstatus = '3'
        <if test="name != null and name != ''">
            and b.name like '%' || #{name} || '%'
        </if>
        <if test="credittype != null and credittype != ''">
            and a.credittype = #{credittype}
        </if>
    </select>
    <select id="selectInfoById" resultType="io.renren.modules.regul.cd02.dto.Cd02DTO">
        select a.credittype,
               b.corpname as name,
               b.corpcode as uniquecode,
               (select x.name from b_pj01 x where x.pj0101 = t.pj0101) as projectname,
               t.*
          from B_CD02 t, b_cd01 a, b_cp01 b
         where t.cd0101 = a.cd0101
           and t.cd0201 = #{cd0201}
           and a.credittype = '1'
           and a.creditno = b.cp0101
        union all
        select a.credittype,
               b.teamname as name,
               b.teamsysno as uniquecode,
               (select x.name from b_pj01 x where x.pj0101 = t.pj0101) as projectname,
               t.*
          from B_CD02 t, b_cd01 a, b_tm01 b
         where t.cd0101 = a.cd0101
           and t.cd0201 = #{cd0201}
           and a.credittype = '2'
           and a.creditno = b.tm0101
        union all
        select a.credittype,
               b.name as name,
               b.idcardnumber as uniquecode,
               (select x.name from b_pj01 x where x.pj0101 = t.pj0101) as projectname,
               t.*
          from B_CD02 t, b_cd01 a, b_ps01 b
         where t.cd0101 = a.cd0101
           and t.cd0201 = #{cd0201}
           and a.credittype = '3'
           and a.creditno = b.ps0101
    </select>
</mapper>