
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自定义日期选择器</title>
    <!-- Vue2 CDN - 使用多个备用CDN -->
    <script src="https://cdn.bootcdn.net/ajax/libs/vue/2.6.14/vue.min.js"></script>
    <script>
        // 如果第一个CDN失败，尝试备用CDN
        if (typeof Vue === 'undefined') {
            document.write('<script src="https://unpkg.com/vue@2.6.14/dist/vue.min.js"><\/script>');
        }
    </script>
    <!-- Element UI CSS - 使用多个备用CDN -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/element-ui/2.15.14/theme-chalk/index.css">
    <script>
        // 如果CSS加载失败，添加备用样式
        if (!document.querySelector('link[href*="element-ui"]')) {
            document.write('<link rel="stylesheet" href="https://unpkg.com/element-ui@2.15.14/lib/theme-chalk/index.css">');
        }
    </script>
    <!-- Element UI JS - 使用多个备用CDN -->
    <script src="https://cdn.bootcdn.net/ajax/libs/element-ui/2.15.14/index.js"></script>
    <script>
        // 如果Element UI加载失败，尝试备用CDN
        if (typeof ELEMENT === 'undefined') {
            document.write('<script src="https://unpkg.com/element-ui@2.15.14/lib/index.js"><\/script>');
        }
    </script>
    <style>
        .custom-datepicker-container {
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .datepicker-header {
            margin-bottom: 20px;
        }
        
        .datepicker-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #303133;
        }
        
        .datepicker-input {
            margin-bottom: 20px;
        }
        
        .custom-panel {
            position: relative;
            display: inline-block;
        }
        
        .custom-panel-content {
            position: absolute;
            top: 100%;
            left: 0;
            z-index: 1000;
            background: #fff;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 0;
            min-width: 500px;
            display: none;
        }
        
        .custom-panel-content.show {
            display: block;
        }
        
        .panel-layout {
            display: flex;
            height: 400px;
        }
        
        .left-sidebar {
            width: 100px;
            border-right: 1px solid #e4e7ed;
            background: #fafafa;
            padding: 0;
        }
        
        .sidebar-item {
            padding: 15px 20px;
            cursor: pointer;
            text-align: center;
            font-size: 14px;
            color: #606266;
            transition: all 0.3s;
            border-bottom: 1px solid #e4e7ed;
            position: relative;
        }
        
        .sidebar-item:last-child {
            border-bottom: none;
        }
        
        .sidebar-item:hover {
            background-color: #f5f7fa;
            color: #409eff;
        }
        
        .sidebar-item.active {
            background-color: #ecf5ff;
            color: #409eff;
            font-weight: bold;
        }
        
        .sidebar-item.active::after {
            content: '';
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 20px;
            background-color: #409eff;
        }
        
        .right-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            position: relative;
            overflow: hidden;
        }
        
        .content-section {
            display: none;
            flex: 1;
            overflow: hidden;
        }
        
        .content-section.show {
            display: flex;
            flex-direction: column;
        }
        
        /* Element UI 日期选择器样式覆盖 */
        .element-picker {
            height: 100%;
            width: 100%;
            position: relative;
        }
        
        /* 隐藏输入框 */
        .element-picker .el-input {
            display: none !important;
        }
        
        /* 强制显示选择器面板 */
        .element-picker .el-picker-panel {
            position: static !important;
            box-shadow: none !important;
            border: none !important;
            margin: 0 !important;
            width: 100% !important;
            height: 100% !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }
        
        .element-picker .el-picker-panel__body {
            margin: 0 !important;
            padding: 0 !important;
            height: 100% !important;
        }
        
        .element-picker .el-picker-panel__content {
            height: 100% !important;
        }
        
        .element-picker .el-date-picker__header {
            margin: 0 !important;
            padding: 12px 20px !important;
            border-bottom: 1px solid #e4e7ed !important;
        }
        
        .element-picker .el-picker-panel__body-wrapper {
            height: calc(100% - 60px) !important;
            overflow: hidden !important;
        }
        
        .element-picker .el-picker-panel__body-inner {
            height: 100% !important;
            overflow-y: auto !important;
        }
        
        /* 面板底部 */
        .panel-footer {
            padding: 12px 20px;
            border-top: 1px solid #e4e7ed;
            text-align: right;
            background: #fafafa;
        }
        
        .result-display {
            margin-top: 20px;
            padding: 15px;
            background: #f5f7fa;
            border-radius: 4px;
        }
        
        .result-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #303133;
        }
        
        .result-content {
            color: #606266;
        }
        
        /* 调试样式 */
        .debug-info {
            margin-top: 20px;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 4px;
            font-size: 12px;
        }
        
        /* 备用样式 - 当Element UI加载失败时使用 */
        .el-input {
            position: relative;
            font-size: 14px;
            display: inline-block;
            width: 100%;
        }
        
        .el-input__inner {
            -webkit-appearance: none;
            background-color: #fff;
            background-image: none;
            border-radius: 4px;
            border: 1px solid #dcdfe6;
            box-sizing: border-box;
            color: #606266;
            display: inline-block;
            font-size: inherit;
            height: 40px;
            line-height: 40px;
            outline: none;
            padding: 0 15px;
            transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            width: 100%;
        }
        
        .el-input__inner:focus {
            outline: none;
            border-color: #409eff;
        }
        
        .el-input__suffix {
            position: absolute;
            height: 100%;
            right: 5px;
            top: 0;
            text-align: center;
            color: #c0c4cc;
            transition: all 0.3s;
            pointer-events: none;
        }
        
        .el-input__icon {
            height: 100%;
            width: 25px;
            text-align: center;
            transition: all 0.3s;
            line-height: 40px;
        }
        
        .el-button {
            display: inline-block;
            line-height: 1;
            white-space: nowrap;
            cursor: pointer;
            background: #fff;
            border: 1px solid #dcdfe6;
            color: #606266;
            -webkit-appearance: none;
            text-align: center;
            box-sizing: border-box;
            outline: none;
            margin: 0;
            transition: 0.1s;
            font-weight: 500;
            padding: 12px 20px;
            font-size: 14px;
            border-radius: 4px;
        }
        
        .el-button:hover {
            color: #409eff;
            border-color: #c6e2ff;
            background-color: #ecf5ff;
        }
        
        .el-button--small {
            padding: 9px 15px;
            font-size: 12px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="custom-datepicker-container">
            <div class="datepicker-header">
                <div class="datepicker-title">自定义日期选择器</div>
            </div>
            
            <div class="datepicker-input">
                <div class="el-input" @click="togglePanel" style="width: 300px; cursor: pointer;">
                    <input 
                        class="el-input__inner" 
                        v-model="displayValue"
                        placeholder="请选择日期"
                        readonly
                    >
                    <span class="el-input__suffix">
                        <i class="el-input__icon el-icon-date">📅</i>
                    </span>
                </div>
            </div>
            
            <div class="custom-panel">
                <div class="custom-panel-content" :class="{ show: showPanel }">
                    <div class="panel-layout">
                        <!-- 左侧快捷选项 -->
                        <div class="left-sidebar">
                            <div 
                                class="sidebar-item" 
                                :class="{ active: activeTab === 'year' }"
                                @click="switchTab('year')"
                            >
                                年份
                            </div>
                            <div 
                                class="sidebar-item" 
                                :class="{ active: activeTab === 'month' }"
                                @click="switchTab('month')"
                            >
                                月份
                            </div>
                            <div 
                                class="sidebar-item" 
                                :class="{ active: activeTab === 'date' }"
                                @click="switchTab('date')"
                            >
                                日期
                            </div>
                        </div>
                        
                        <!-- 右侧内容区域 -->
                        <div class="right-content">
                            <!-- 年份选择 - 使用Element UI年份选择器 -->
                            <div class="content-section" :class="{ show: activeTab === 'year' }">
                                <div class="element-picker">
                                    <el-date-picker
                                        ref="yearPicker"
                                        v-model="selectedYearValue"
                                        type="year"
                                        placeholder="选择年份"
                                        format="yyyy"
                                        value-format="yyyy"
                                        @change="onYearChange"
                                        :visible="yearPickerVisible"
                                        @visible-change="onYearPickerVisibleChange"
                                        style="width: 100%; height: 100%;">
                                    </el-date-picker>
                                </div>
                            </div>
                            
                            <!-- 月份选择 - 使用Element UI月份选择器 -->
                            <div class="content-section" :class="{ show: activeTab === 'month' }">
                                <div class="element-picker">
                                    <el-date-picker
                                        ref="monthPicker"
                                        v-model="selectedMonthValue"
                                        type="month"
                                        placeholder="选择月份"
                                        format="yyyy-MM"
                                        value-format="yyyy-MM"
                                        @change="onMonthChange"
                                        :visible="monthPickerVisible"
                                        @visible-change="onMonthPickerVisibleChange"
                                        style="width: 100%; height: 100%;">
                                    </el-date-picker>
                                </div>
                            </div>
                            
                            <!-- 日期选择 - 使用Element UI日期选择器 -->
                            <div class="content-section" :class="{ show: activeTab === 'date' }">
                                <div class="element-picker">
                                    <el-date-picker
                                        ref="datePicker"
                                        v-model="selectedDateValue"
                                        type="date"
                                        placeholder="选择日期"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        @change="onDateChange"
                                        :visible="datePickerVisible"
                                        @visible-change="onDatePickerVisibleChange"
                                        style="width: 100%; height: 100%;">
                                    </el-date-picker>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="panel-footer">
                        <button class="el-button el-button--small" @click="clearSelection">清空</button>
                        <button class="el-button el-button--small" @click="confirmSelection">确定</button>
                    </div>
                </div>
            </div>
            
            <!-- 结果显示 -->
            <div class="result-display">
                <div class="result-title">选择结果：</div>
                <div class="result-content">
                    <p><strong>选中年份：</strong>{{ selectedYearValue || "未选择" }}</p>
                    <p><strong>选中月份：</strong>{{ selectedMonthValue || "未选择" }}</p>
                    <p><strong>选中日期：</strong>{{ selectedDateValue || "未选择" }}</p>
                    <p><strong>完整日期：</strong>{{ finalDate || "未选择" }}</p>
                </div>
            </div>
            
            <!-- 调试信息 -->
            <div class="debug-info">
                <p>Vue状态: {{ vueLoaded ? '已加载' : '未加载' }}</p>
                <p>Element UI状态: {{ elementLoaded ? '已加载' : '未加载' }}</p>
                <p>面板状态: {{ showPanel ? '显示' : '隐藏' }}</p>
                <p>当前标签: {{ activeTab }}</p>
                <p>年份选择器显示: {{ yearPickerVisible ? '是' : '否' }}</p>
                <p>月份选择器显示: {{ monthPickerVisible ? '是' : '否' }}</p>
                <p>日期选择器显示: {{ datePickerVisible ? '是' : '否' }}</p>
            </div>
        </div>
    </div>

    <script>
        // 等待所有资源加载完成
        window.addEventListener('load', function() {
            console.log('Page loaded, checking dependencies...');
            
            // 检查Vue是否加载成功
            var vueLoaded = typeof Vue !== 'undefined';
            var elementLoaded = typeof ELEMENT !== 'undefined';
            
            console.log('Vue loaded:', vueLoaded);
            console.log('Element UI loaded:', elementLoaded);
            
            if (!vueLoaded) {
                console.error('Vue未加载成功，请检查网络连接');
                document.querySelector('.debug-info').innerHTML += '<p style="color: red;">Vue加载失败</p>';
                return;
            }
            
            new Vue({
                el: '#app',
                data() {
                    // 获取当前日期
                    const now = new Date();
                    const currentYear = now.getFullYear();
                    const currentMonth = now.getMonth() + 1;
                    const currentDate = now.getDate();
                    
                    return {
                        showPanel: false,
                        activeTab: 'year',
                        displayValue: '',
                        
                        // 选中的值 - 默认设置为当前年月日
                        selectedYearValue: currentYear.toString(), // 默认当前年份
                        selectedMonthValue: currentYear + '-' + (currentMonth < 10 ? '0' + currentMonth : currentMonth), // 默认当前月份
                        selectedDateValue: currentYear + '-' + (currentMonth < 10 ? '0' + currentMonth : currentMonth) + '-' + (currentDate < 10 ? '0' + currentDate : currentDate), // 默认当前日期
                        finalDate: currentYear + '-' + (currentMonth < 10 ? '0' + currentMonth : currentMonth) + '-' + (currentDate < 10 ? '0' + currentDate : currentDate),
                        
                        // 控制选择器显示
                        yearPickerVisible: false,
                        monthPickerVisible: false,
                        datePickerVisible: false,
                        
                        // 调试信息
                        vueLoaded: vueLoaded,
                        elementLoaded: elementLoaded
                    }
                },
                methods: {
                    // 切换面板显示
                    togglePanel() {
                        console.log('togglePanel called, current state:', this.showPanel);
                        this.showPanel = !this.showPanel;
                        if (this.showPanel) {
                            this.activeTab = 'year';
                            this.openCurrentPicker();
                        }
                    },
                    
                    // 切换标签页
                    switchTab(tab) {
                        console.log('switchTab called:', tab);
                        this.activeTab = tab;
                        this.openCurrentPicker();
                    },
                    
                    // 打开当前对应的选择器
                    openCurrentPicker() {
                        // 先关闭所有选择器
                        this.yearPickerVisible = false;
                        this.monthPickerVisible = false;
                        this.datePickerVisible = false;
                        
                        // 延迟打开当前选择器，确保DOM更新完成
                        this.$nextTick(() => {
                            setTimeout(() => {
                                if (this.activeTab === 'year') {
                                    this.yearPickerVisible = true;
                                    this.forceShowPicker('yearPicker');
                                } else if (this.activeTab === 'month') {
                                    this.monthPickerVisible = true;
                                    this.forceShowPicker('monthPicker');
                                } else if (this.activeTab === 'date') {
                                    this.datePickerVisible = true;
                                    this.forceShowPicker('datePicker');
                                }
                            }, 100);
                        });
                    },
                    
                    // 强制显示选择器
                    forceShowPicker(pickerRef) {
                        this.$nextTick(() => {
                            const picker = this.$refs[pickerRef];
                            if (picker) {
                                console.log('Forcing show picker:', pickerRef);
                                
                                // 方法1: 直接调用内部方法
                                if (picker.handleFocus) {
                                    picker.handleFocus();
                                }
                                
                                // 方法2: 触发输入框点击事件
                                const input = picker.$el.querySelector('.el-input__inner');
                                if (input) {
                                    input.click();
                                    input.focus();
                                }
                                
                                // 方法3: 触发鼠标事件
                                const inputWrapper = picker.$el.querySelector('.el-input');
                                if (inputWrapper) {
                                    const clickEvent = new MouseEvent('click', {
                                        view: window,
                                        bubbles: true,
                                        cancelable: true
                                    });
                                    inputWrapper.dispatchEvent(clickEvent);
                                }
                                
                                // 方法4: 直接设置内部状态
                                if (picker.pickerVisible !== undefined) {
                                    picker.pickerVisible = true;
                                }
                                
                                // 方法5: 触发键盘事件
                                const keyEvent = new KeyboardEvent('keydown', {
                                    key: 'Enter',
                                    code: 'Enter',
                                    keyCode: 13,
                                    which: 13,
                                    bubbles: true
                                });
                                if (input) {
                                    input.dispatchEvent(keyEvent);
                                }
                                
                                // 方法6: 使用Element UI的内部方法
                                if (picker.$refs && picker.$refs.picker) {
                                    picker.$refs.picker.visible = true;
                                }
                                
                                // 方法7: 直接操作DOM
                                const pickerPanel = picker.$el.querySelector('.el-picker-panel');
                                if (pickerPanel) {
                                    pickerPanel.style.display = 'block';
                                    pickerPanel.style.visibility = 'visible';
                                    pickerPanel.style.opacity = '1';
                                }
                            }
                        });
                    },
                    
                    // Element UI年份选择器变化
                    onYearChange(value) {
                        console.log('Element UI year change:', value);
                        this.selectedYearValue = value;
                        this.updateFinalDate();
                    },
                    
                    // Element UI月份选择器变化
                    onMonthChange(value) {
                        console.log('Element UI month change:', value);
                        this.selectedMonthValue = value;
                        this.updateFinalDate();
                    },
                    
                    // Element UI日期选择器变化
                    onDateChange(value) {
                        console.log('Element UI date change:', value);
                        this.selectedDateValue = value;
                        this.updateFinalDate();
                    },
                    
                    // 年份选择器显示状态变化
                    onYearPickerVisibleChange(visible) {
                        console.log('Year picker visible change:', visible);
                        this.yearPickerVisible = visible;
                    },
                    
                    // 月份选择器显示状态变化
                    onMonthPickerVisibleChange(visible) {
                        console.log('Month picker visible change:', visible);
                        this.monthPickerVisible = visible;
                    },
                    
                    // 日期选择器显示状态变化
                    onDatePickerVisibleChange(visible) {
                        console.log('Date picker visible change:', visible);
                        this.datePickerVisible = visible;
                    },
                    
                    // 更新最终日期
                    updateFinalDate() {
                        if (this.selectedDateValue) {
                            // 如果选择了完整日期，使用完整日期
                            this.finalDate = this.selectedDateValue;
                            this.displayValue = this.finalDate;
                        } else if (this.selectedMonthValue) {
                            // 如果只选择了月份，显示年月
                            this.finalDate = this.selectedMonthValue;
                            this.displayValue = this.finalDate;
                        } else if (this.selectedYearValue) {
                            // 如果只选择了年份，显示年份
                            this.finalDate = this.selectedYearValue;
                            this.displayValue = this.finalDate;
                        } else {
                            this.finalDate = '';
                            this.displayValue = '';
                        }
                        console.log('Final date updated:', this.finalDate);
                    },
                    
                    // 清空选择
                    clearSelection() {
                        console.log('clearSelection called');
                        this.selectedYearValue = '';
                        this.selectedMonthValue = '';
                        this.selectedDateValue = '';
                        this.finalDate = '';
                        this.displayValue = '';
                    },
                    
                    // 确认选择
                    confirmSelection() {
                        console.log('confirmSelection called');
                        this.showPanel = false;
                    }
                },
                mounted() {
                    console.log('Vue instance mounted');
                    // 初始化显示值
                    this.updateFinalDate();
                    
                    // 点击外部关闭面板
                    document.addEventListener('click', (e) => {
                        if (!this.$el.contains(e.target)) {
                            this.showPanel = false;
                        }
                    });
                }
            });
        });
    </script>
</body>
</html>
