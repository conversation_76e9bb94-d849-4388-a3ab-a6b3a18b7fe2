package io.renren.modules.regul.pw01.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.regul.ps12.dao.Ps12Dao;
import io.renren.modules.regul.ps12.dto.Ps12DTO;
import io.renren.modules.regul.ps12.entity.Ps12Entity;
import io.renren.modules.regul.pw01.dao.Pw01Dao;
import io.renren.modules.regul.pw01.dto.Pw01DTO;
import io.renren.modules.regul.pw01.entity.Pw01Entity;
import io.renren.modules.regul.pw01.service.Pw01Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 工资导入记录表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-06-04
 */
@Service
public class Pw01ServiceImpl extends CrudServiceImpl<Pw01Dao, Pw01Entity, Pw01DTO> implements Pw01Service {
    @Autowired
    private Ps12Dao ps12Dao;

    @Override
    public QueryWrapper<Pw01Entity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Pw01Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    public PageData<Pw01DTO> pageList(Map<String, Object> params) {
        long curPage = Long.parseLong((String) params.get(Constant.PAGE));
        long limit = Long.parseLong((String) params.get(Constant.LIMIT));
        Ps12DTO ps12 = ps12Dao.getByUserId(SecurityUser.getUser().getId());
        if (!Objects.isNull(ps12)) {
            params.put("ps1201", ps12.getPs1201());
        }
        Page<Pw01DTO> page = new Page<>(curPage, limit);
        List<Pw01DTO> list = baseDao.getList(page, params);
        return getPageData(list, page.getTotal(), Pw01DTO.class);
    }
}