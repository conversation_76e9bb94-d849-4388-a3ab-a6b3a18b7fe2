package io.renren.modules.regul.pj01collect.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 监管用户项目收藏表
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-01-19
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_PJ01_COLLECT")
public class Pj01CollectEntity  {
	private static final long serialVersionUID = 1L;

    /**
     * $column.comments
     */
	private Long id;
    /**
     * 监管用户id
     */
	private Long userId;
    /**
     * 项目Id
     */
	private Long pj0101;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
}