package io.renren.modules.regul.kq02.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.regul.kq02.dao.Kq02Dao;
import io.renren.modules.regul.kq02.dto.Kq02DTO;
import io.renren.modules.regul.kq02.dto.Kq02PageDTO;
import io.renren.modules.regul.kq02.entity.Kq02Entity;
import io.renren.modules.regul.kq02.service.Kq02Service;
import io.renren.modules.regul.pj01.dao.Pj01Dao;
import io.renren.modules.regul.statistics.dto.Attendance43StatisticsDTO;
import io.renren.modules.regul.statistics.dto.AttendanceDayDTO;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 建筑工人考勤信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Service
public class Kq02ServiceImpl extends CrudServiceImpl<Kq02Dao, Kq02Entity, Kq02DTO> implements Kq02Service {
    @Autowired
    private Pj01Dao pj01Dao;

    @Override
    public QueryWrapper<Kq02Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<Kq02Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    public PageData<Kq02PageDTO> pageList(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId());
        IPage<Kq02Entity> page = getPage(params, "CHECKDATE", false);
        List<Kq02PageDTO> list = baseDao.getListData(params);
        return getPageData(list, page.getTotal(), Kq02PageDTO.class);
    }

    @Override
    public void exportList(Map<String, Object> params, HttpServletResponse response) throws IOException {
        List<Attendance43StatisticsDTO> datalist = baseDao.exportList();
        for (Attendance43StatisticsDTO dto : datalist) {
            List<AttendanceDayDTO> kqlist = pj01Dao.getAttendanceDayList(dto.getUserId(), "2023-11-01", "2023-12-21");
            dto.setKqlist(kqlist);
        }
        ArrayList<ExcelExportEntity> colList = new ArrayList<>();
        ExcelExportEntity colEntity = new ExcelExportEntity("序号", "sno");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("项目名称", "projectname");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("姓名", "name");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("证件号码", "idcardnumber");
        colEntity.setNeedMerge(false);
        colEntity.setWidth(30);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("岗位", "jobtype");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("入场时间", "entrytime");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("录入系统时间", "createDate");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("考勤天数", "kqs");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("选择天数", "days");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("今日考勤", "todaykqs");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("到岗率", "arrivepercent");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        ExcelExportEntity dateColGroup = new ExcelExportEntity("考勤日期", "kqlist");
        List<ExcelExportEntity> dateColList = new ArrayList<ExcelExportEntity>();
        for (AttendanceDayDTO dto : datalist.get(0).getKqlist()) {
            dateColList.add(new ExcelExportEntity(dto.getDay(), dto.getDay()));
        }
        dateColGroup.setList(dateColList);
        colList.add(dateColGroup);
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        //存储每一行中的日期数据
        List<Map<String, Object>> dataListChild = null;
        //存储表格中的每一行数据
        Map<String, Object> valMap = null;
        for (Attendance43StatisticsDTO dto : datalist) {
            valMap = new HashMap(12);
            valMap.put("sno", dto.getSno());
            valMap.put("projectname", dto.getProjectname());
            valMap.put("name", dto.getName());
            valMap.put("idcardnumber", dto.getIdcardnumber());
            valMap.put("jobtype", dto.getJobtype());
            valMap.put("entrytime", dto.getEntrytime());
            valMap.put("createDate", dto.getCreateDate());
            valMap.put("kqs", dto.getKqs());
            valMap.put("days", dto.getDays());
            valMap.put("todaykqs", dto.getTodaykqs());
            valMap.put("arrivepercent", dto.getArrivepercent());
            dataListChild = new ArrayList<>();
            Map<String, Object> dateMap = new HashMap<String, Object>();
            for (AttendanceDayDTO attendanceDayDTO : dto.getKqlist()) {
                dateMap.put(attendanceDayDTO.getDay(), attendanceDayDTO.getIskq());
            }
            dataListChild.add(dateMap);
            valMap.put("kqlist", dataListChild);
            list.add(valMap);
        }
        response.setCharacterEncoding("UTF-8");
        response.setHeader("content-Type", "application/vnd.ms-excel");
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode("管理人员到岗详情统计", "UTF-8") + ".xls");
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("管理人员到岗详情统计", "数据"), colList,
                list);
        ServletOutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        workbook.close();
    }
}