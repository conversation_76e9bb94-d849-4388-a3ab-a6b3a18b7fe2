package io.renren.modules.regul.ps04merge.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.Result;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.regul.cp02.dao.Cp02Dao;
import io.renren.modules.regul.cp02.entity.Cp02Entity;
import io.renren.modules.regul.pj01.dao.Pj01Dao;
import io.renren.modules.regul.pj01.dto.Pj01DTO;
import io.renren.modules.regul.pj01.entity.Pj01Entity;
import io.renren.modules.regul.ps04.dao.Ps04Dao;
import io.renren.modules.regul.ps04.dto.Ps04DTO;
import io.renren.modules.regul.ps04.entity.Ps04Entity;
import io.renren.modules.regul.ps04merge.dao.Ps04MergeDao;
import io.renren.modules.regul.ps04merge.dto.Ps04MergeDTO;
import io.renren.modules.regul.ps04merge.entity.Ps04MergeEntity;
import io.renren.modules.regul.ps04merge.service.Ps04MergeService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 管理人员变更表
 *
 * <AUTHOR>
 * @since 1.0.0 2022-12-29
 */
@Service
public class Ps04MergeServiceImpl extends CrudServiceImpl<Ps04MergeDao, Ps04MergeEntity, Ps04MergeDTO> implements Ps04MergeService {
    @Autowired
    private Pj01Dao pj01Dao;
    @Autowired
    private Ps04Dao ps04Dao;
    @Autowired
    private Cp02Dao cp02Dao;

    @Override
    public QueryWrapper<Ps04MergeEntity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<Ps04MergeEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

    @Override
    public PageData<Ps04MergeDTO> pageList(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId());
        IPage<Ps04MergeEntity> page = getPage(params, "", false);
        List<Ps04MergeDTO> list = baseDao.pageList(params);
        return getPageData(list, page.getTotal(), Ps04MergeDTO.class);
    }

    @Override
    public Result getInfo(Map<String, Object> params) {
        Result<Object> result = new Result<>();
        Long id = Long.valueOf(params.get("id").toString());
        //查询审核信息
        Ps04MergeDTO dto = baseDao.selectInfoById(id);
        //查询合并管理人员列表
        String[] split = dto.getPs0401s().split(",");
        ArrayList<String> ps0401s = CollectionUtil.toList(dto.getPs0401s().split(","));
        List<Ps04DTO> ps04s = ps04Dao.selectListByIds(ps0401s);
        dto.setPs04s(ps04s);
        //查询合并项目列表
        ArrayList<String> pj0101s = CollectionUtil.toList(dto.getPj0101s().split(","));
        List<Pj01DTO> pj01s = pj01Dao.selectListByIds(pj0101s);
        dto.setPj01s(pj01s);
        return result.ok(dto);
    }

    @Override
    public Result audit(Ps04MergeDTO dto) {
        Result<Object> result = new Result<>();
        Ps04MergeEntity ps04Merge = baseDao.selectById(dto.getId());
        if (!"0".equals(ps04Merge.getAuditstatus())) {
            return result.error("该数据已审核，请勿重复审核！");
        }
        if ("1".equals(dto.getAuditstatus())) {
            //查询合并管理人员
            List<Ps04Entity> ps04s = ps04Dao.selectBatchIds(CollectionUtil.toList(dto.getPs0401s().split(",")));
            //查询合并项目
            List<Pj01Entity> pj01s = pj01Dao.selectBatchIds(CollectionUtil.toList(dto.getPj0101s().split(",")));
            //将管理人员合并至新项目
            for (Pj01Entity pj01 : pj01s) {
                for (Ps04Entity ps04 : ps04s) {
                    //查询该人员在新项目中的参建单位
                    Cp02Entity cp02 = cp02Dao.selectForPs04(pj01.getPj0101(), ps04.getCp0201());
                    if (cp02 == null) {
                        return result.error("人员合并参建单位不存在或与原单位不符！");
                    }
                    List<Ps04Entity> isps04 = ps04Dao.selectList(new QueryWrapper<Ps04Entity>().eq("PJ0101", pj01.getPj0101()).eq("PS0301", ps04.getPs0301()));
                    if (isps04.size() > 0) {
                        return result.error("管理人员重复任职！");
                    }
                    Ps04Entity newPs04 = new Ps04Entity();
                    BeanUtil.copyProperties(ps04, newPs04, CopyOptions.create().setIgnoreNullValue(true));
                    newPs04.setPj0101(pj01.getPj0101());
                    newPs04.setCp0201(cp02.getCp0201());
                    newPs04.setEntrytime(new Date());
                    ps04Dao.insert(newPs04);
                }
            }
        }
        BeanUtil.copyProperties(dto, ps04Merge, CopyOptions.create().setIgnoreNullValue(true));
        baseDao.updateById(ps04Merge);
        return result;
    }
}