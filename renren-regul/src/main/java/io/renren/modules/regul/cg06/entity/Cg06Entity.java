package io.renren.modules.regul.cg06.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 区域档案上传配置
 *
 * @<NAME_EMAIL>
 * @since 3.0 2023-11-06
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_CG06")
public class Cg06Entity {
	private static final long serialVersionUID = 1L;

	/**
	* 主键ID
	*/
	@TableId
	private Long cg0601;
	/**
	* 项目id
	*/
	private Long pj0101;
	/**
	* 阶段id
	*/
	private Long cg0401;
	/**
	* 创建日期
	*/
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 *0允许修改，1不允许修改
	 */
	private String cg0602;
}