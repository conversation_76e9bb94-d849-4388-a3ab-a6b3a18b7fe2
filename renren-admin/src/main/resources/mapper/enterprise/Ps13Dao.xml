<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.ps13.dao.Ps13Dao">
    <select id="countByCp0201AndJobType" resultType="java.lang.Long">
        select count(1)
        from b_ps13 t
                 inner join b_cp02 t1
                            on t.corptype = t1.corptype
        where t.jobtype = #{jobType}
          and t1.cp0201 = #{cp0201}
    </select>
</mapper>