<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.cg06.dao.Cg06Dao">
    <update id="saveIsUpdate">
        update b_cg06 set cg0602 = #{cg0602} where cg0601 = #{cg0601}
    </update>
    <delete id="deleteByPj0101">
        delete
        from b_cg06
        where pj0101 = #{pj0101}
    </delete>

    <select id="getFileTree" resultType="io.renren.modules.enterprise.cg06.dto.FileTreeDTO">
        select a.CG0401 as key, b.dict_label as label
        from b_cg04 a, (select dict_label, dict_value
            from sys_dict_data
            where DICT_TYPE_ID = '1925079504290246657') b
        where a.cg0402 = b.dict_value
          and a.cg0403 = '1'
        order by cg0402
    </select>
    <select id="getList" resultType="io.renren.modules.enterprise.cg06.dto.Cg06DTO">
        select pj0101,name from b_pj01
        where 1 = 1
        <if test="pj0101 != '' and pj0101 != null">
            and pj0101 = #{pj0101}
        </if>
        <if test="name != '' and name != null">
            and name like '%' || #{name} || '%'
        </if>
    </select>
    <select id="getFiles" resultType="io.renren.modules.enterprise.cg06.dto.Cg06FileNumsDTO">
        select main_t.cg0601,
        main_t.cg0401,
        b.pj0101,
        main_t.cg0602,
        c.dict_label as stagename,
        nvl(file_counts.shouldFiles, 0) as shouldFiles,
        nvl(file_uploads.uploadedFiles, 0) as uploadedFiles
        from b_cg06 main_t
        join b_pj01 b
        on main_t.pj0101 = b.pj0101
        join b_cg04 x
        on main_t.cg0401 = x.cg0401
        join (select dict_label, dict_value
        from sys_dict_data
        where dict_type_id = '1925079504290246657') c
        on x.cg0402 = c.dict_value
        left join (select cg0601, count(1) as shouldfiles
        from b_cg07
        group by cg0601) file_counts
        on main_t.cg0601 = file_counts.cg0601
        left join (select t1.cg0601, count(distinct t1.cg0701) as uploadedfiles
        from b_cg07 t1
        join b_cg08 t2
        on t1.cg0701 = t2.cg0701
        and t2.cg0802 = '1'
        group by t1.cg0601) file_uploads
        on main_t.cg0601 = file_uploads.cg0601
        <if test="pj0101 != '' and pj0101 != null">
            where main_t.pj0101 = #{pj0101}
        </if>
        order by x.cg0402
    </select>
    <select id="getPj0101" resultType="java.lang.Long">
        select pj0101
        from b_pj01
        where dept_id = #{deptId}
    </select>
    <select id="getBcg05List" resultType="io.renren.modules.enterprise.cg06.dto.TransferDTO">
        select c.cg0501 as key ,d.dict_label as label
        from b_cg06 a, b_cg07 b, b_cg05 c, (select dict_label, dict_value
            from sys_dict_data
            where DICT_TYPE_ID = '1265521283182740000') d
        where a.cg0601 = b.cg0601
          and b.cg0501 = c.cg0501
          and c.cg0502 = d.dict_value
          and a.pj0101 = #{pj0101}
    </select>
    <select id="getFilesList" resultType="io.renren.modules.enterprise.cg05.dto.Cg05DisplayDTO">
        SELECT *
        FROM (
                 SELECT
                     a.CG0502,
                     a.CG0503,
                     a.CG0504,
                     a.CG0505,
                     a.CG0508,
                     c.cg0701,
                     d.cg0801,
                     d.cg0802,
                     e.ot0101,
                     d.uploaddate,
                     ROW_NUMBER() OVER (PARTITION BY c.cg0701 ORDER BY d.uploaddate DESC NULLS LAST) AS rn
                 FROM b_cg05 a
                          JOIN b_cg07 c  ON a.cg0501 = c.cg0501
                          JOIN b_cg06 b  ON b.cg0601 = c.cg0601
                          LEFT JOIN b_cg08 d ON d.cg0701 = c.cg0701
                          LEFT JOIN b_ot01 e ON e.busisysno = c.cg0501
                 WHERE b.cg0601 = #{cg0601}
             ) t
        WHERE rn = 1
        ORDER BY cg0701
    </select>
    <select id="getSpecifyFilesList" resultType="io.renren.modules.enterprise.pj01info.dto.Ot01DTO">
        select * from b_ot01 a where busisysno = #{cg0801} and whether = '1'
    </select>
    <select id="saveGeneralStage" parameterType="java.util.Map" statementType="CALLABLE">
        {
            call insert_cg06_cg07(
                #{p_pj0101,jdbcType=VARCHAR,mode=IN},
                #{p_type,jdbcType=VARCHAR,mode=IN},
                #{p_code,jdbcType=VARCHAR,mode=OUT},
                #{p_message,jdbcType=VARCHAR,mode=OUT}
                 )
            }
    </select>
    <select id="getIsUpdate" resultType="io.renren.modules.enterprise.cg06.dto.StageIsUpdateDTO">
        select cg0601,cg0602 from b_cg06 where cg0601 = #{cg0601}
    </select>
    <select id="getMonthFilesList" resultType="io.renren.modules.enterprise.cg05.dto.Cg05DisplayDTO">
        WITH mon AS (
            SELECT TO_CHAR(ADD_MONTHS(TRUNC(TO_DATE(#{startDate},'YYYYMM'),'MM'),LEVEL-1),'YYYYMM') AS ssny
            FROM dual
        CONNECT BY ADD_MONTHS(TRUNC(TO_DATE(#{startDate},'YYYYMM'),'MM'),LEVEL-1) &lt;= TRUNC(TO_DATE(#{endDate},'YYYYMM'),'MM')
            ),
            dim AS (
        SELECT a.cg0502,
            a.cg0504,
            a.cg0508,
            c.cg0701
        FROM b_cg05  a
            JOIN b_cg07  c ON c.cg0501 = a.cg0501
            JOIN b_cg06  b ON b.cg0601 = c.cg0601
        WHERE c.cg0701 = #{cg0701}
            )
        SELECT m.ssny,
               d.cg0502,
               d.cg0504,
               d.cg0508,
               d.cg0701,
               f.cg0801,
               f.cg0802,
               f.uploaddate
        FROM mon m
                 LEFT JOIN dim d ON 1 = 1
                 LEFT JOIN b_cg08 f
                           ON f.cg0701 = d.cg0701
                               AND f.ssny = m.ssny
        ORDER BY m.ssny
    </select>

</mapper>