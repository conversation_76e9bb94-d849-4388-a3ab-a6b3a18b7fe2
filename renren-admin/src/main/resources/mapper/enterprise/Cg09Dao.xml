<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.cg09.dao.Cg09Dao">
    <select id="getFileTreeList" resultType="io.renren.modules.enterprise.cg09.dto.Cg09DTO">
        select a.cg0901            as id,
               a.PID,
               a.FILE_NUMBER       as fileNumber,
               a.FILE_NAME         as fileName,
               a.type,
               a.sort,
               a.TREE_LEVEL        as treeLevel,
               nvl(c.fileCount, 0) as fileCount,
               a.FILE_MODULE       as fileModule,
               a.FILE_TYPE         as fileType
        from b_cg09 a
                 left join (select b.CG0901, count(0) as fileCount
                            from b_cg10 b,
                                 b_ot01 d
                            where b.PJ0101 = #{pj0101}
                              and b.CG1001 = d.BUSISYSNO
                              and d.whether = '1'
                            group by b.CG0901) c
                           on a.CG0901 = c.CG0901
        where a.whether = '1'
        start with a.pid = 0
        connect by prior a.cg0901 = a.pid
        order by a.sort, a.CG0901
    </select>
    <select id="getAllArchiveList" resultType="io.renren.modules.enterprise.cg09.dto.Cg09DTO">
        select a.cg0901,
               a.FILE_MODULE      as fileModule,
               a.FILE_NAME        as fileName,
               a.FILE_NUMBER      as fileNumber,
               a.FILE_TYPE        as fileType,
               (select c.OT0101
                from b_ot01 c
                where c.BUSISYSNO = a.CG0901
                  and c.whether = '1'
                  and c.BUSITYPE = #{fileType}
                  and ROWNUM = 1) as templateUrl
        from B_CG09 a where a.WHETHER = '1'
        <if test="fileModule != null and fileModule != ''">
            and a.file_module = #{fileModule}
        </if>
        <if test="cg0901 != null and cg0901 != ''">
            and a.CG0901 = #{cg0901}
        </if>
    </select>
    <select id="selectConfigInfo" resultType="io.renren.modules.enterprise.cg09.dto.Cg09DTO">
        select a.CG0901,
               a.FILE_MODULE      as fileModule,
               a.FILE_NUMBER      as fileNumber,
               a.FILE_NAME        as fileName,
               (select c.OT0101
                from b_ot01 c
                where c.BUSISYSNO = a.CG0901
                  and c.whether = '1'
                  and c.BUSITYPE = #{fileType}
                  and ROWNUM = 1) as templateUrl
        from b_cg09 a
        where a.cg0901 = #{cg0901}
        <!--以防出现预期之外的数据-->
        and rownum = 1
    </select>
    <select id="getFileCountByPj0101" resultType="io.renren.modules.enterprise.cg10.dto.FileCountInfo">
        select b.cg0901, b.file_year as fileYear, count(c.busisysno) as fileCount
        from b_cg09 a
                 inner join b_cg10 b
                            on a.cg0901 = b.cg0901
                                and a.type = '1' and a.whether = '1' and b.pj0101 = #{pj0101}
                 inner join b_ot01 c
                            on b.cg1001 = c.busisysno
                                and c.whether = '1'
        group by b.cg0901, b.file_year
    </select>
</mapper>