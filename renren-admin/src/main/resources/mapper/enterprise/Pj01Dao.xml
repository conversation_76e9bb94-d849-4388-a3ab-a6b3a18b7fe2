<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pj01.dao.Pj01Dao">
    <select id="getListData" resultType="io.renren.modules.enterprise.pj01.dto.Pj01DTO">
        select t.NAME,
               t.PJ0101,
               t.AREACODE,
               t.INDUSTRY,
               t.PRJSTATUS,
               t.LINKMAN,
               t.LINKPHONE,
               t.INVESTTYPE,
               t.CONSTRUCTTYPE,
               t.STARTDATE,
               t.INVEST,
               t.address,
               t.category,
               a.auditstatus,
               a.auditresult,
               nvl(t.WAGES_STATE, '0') as wagesState
        from B_PJ01 t
                 left join (select t.*
                            from b_pj01_region t
                            where t.id in
                                  (select max(t.id) from B_PJ01_REGION t group by t.pj0101)) a
                           on t.pj0101 = a.pj0101
        where t.pj0101 = #{pj0101}
        <if test="prjstatus != '' and prjstatus != null">
            and t.PRJSTATUS = #{prjstatus}
        </if>
        <if test="name != '' and name != null">
            and t.NAME like '%' || #{name} || '%'
        </if>
    </select>
    <select id="loadUserProjectInfo" resultType="io.renren.modules.enterprise.pj01.dto.Pj01DTO">
        select *
        from B_PJ01 t
        where t.DEPT_ID = #{deptId}
    </select>

    <select id="selectRegionByPj0101" resultType="io.renren.modules.enterprise.pj01.dto.Pj01DTO">
        select t.lat, t.lng, t.region, t.mark_location
        from b_pj01 t
        where t.pj0101 = #{pj0101}
    </select>


    <select id="getExportData" resultType="io.renren.modules.enterprise.pj01.dto.Pj01ExportDataDTO">
        select t.*, c1.*, c11.*, c2.*, c3.*
        from (select t.pj0101, t.name
              from b_pj01 t
              where t.pj0101 = #{pj0101,jdbcType=BIGINT}) t
                 left join (select c.corpname  construcorpname,
                                   c.linkman   construcorpnamelinkman,
                                   c.linkphone construlinkphone,
                                   p.pj0101
                            from b_cp01 c,
                                 b_cp02 p
                            where c.cp0101 = p.cp0101
                              and p.corptype = '9') c1
                           on t.pj0101 = c1.pj0101
                 left join (SELECT t.name construdataname, t.CELLPHONE construdatacellphone, p.pj0101
                            FROM b_ps01 t,
                                 b_ps03 b,
                                 b_ps04 p
                            where t.ps0101 = b.ps0101
                              and b.pj0101 = p.pj0101
                              and b.ps0301 = p.ps0301
                              and p.jobtype = '1022') c11
                           on c11.pj0101 = t.pj0101
                 left join (select c.corpname  ownercorpname,
                                   c.linkman   ownerlinkman,
                                   c.linkphone ownerlinkphone,
                                   p.pj0101
                            from b_cp01 c,
                                 b_cp02 p
                            where c.cp0101 = p.cp0101
                              and p.corptype = '8') c2
                           on t.pj0101 = c2.pj0101
                 left join (select c.corpname  supervisioncorpname,
                                   c.linkman   supervisionlinkman,
                                   c.linkphone supervisionlinkphone,
                                   p.pj0101
                            from b_cp01 c,
                                 b_cp02 p
                            where c.cp0101 = p.cp0101
                              and p.corptype = '3') c3
                           on t.pj0101 = c3.pj0101
    </select>
    <select id="getProjectSupplement" resultType="java.lang.Long">
        select count(1)
        from B_PJ01 t
        where t.pj0101 = #{pj0101}
          and (t.category is null
            or t.constructtype is null
            or t.investtype is null
            or t.linkman is null
            or t.linkphone is null)
    </select>
    <update id="updateWagesState" parameterType="io.renren.modules.enterprise.pj01.dto.Pj01DTO">
        update b_pj01
        set WAGES_STATE = #{wagesState}
        where PJ0101 = #{pj0101}
    </update>
</mapper>