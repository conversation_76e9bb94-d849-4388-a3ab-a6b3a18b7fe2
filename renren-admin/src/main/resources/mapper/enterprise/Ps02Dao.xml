<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.ps02.dao.Ps02Dao">
    <!-- 工人列表查询-->
    <select id="getListData" resultType="io.renren.modules.enterprise.ps02.dto.Ps02PageDTO">
            select h.*
          from (select t.ps0201,
                       t.entrytime,
                       t.exittime,
                       (select x.ps0801 from b_ps08 x where x.ps0201 = t.ps0201) as ps0801,
                       b.name as projectName,
                       t.TM0101,
                       t.pj0101,
                       c.TEAMNAME,
                       (select h.corpname
                          from b_cp01 h
                         where h.cp0101 = (select f.cp0101
                                             from b_tm01 e, b_cp02 f
                                            where e.cp0201 = f.cp0201
                                              and e.tm0101 = t.tm0101)) corpName,
                       a.name personName,
                       a.idcardnumber idCardNumber,
                       t.isteamleader isTeamLeader,
                       a.cellphone cellPhone,
                       t.worktypecode workTypeCode,
                       t.in_or_out inOrOut,
                       '[' || (select case
                                        when count(1) = 0 then
                                         0
                                        else
                                         1
                                      end
                                 from b_ps08 x
                                where x.ps0201 = t.ps0201) || ',' ||
                       (select case
                                 when count(1) = 0 then
                                  0
                                 else
                                  1
                               end
                          from b_ot01 y
                         where y.busisysno = t.ps0201
                           and y.busitype = '10') || ']' as issigned,
                       (select case
                                 when count(1) = 0 then
                                  0
                                 else
                                  1
                               end
                          from b_ot01 y
                         where y.busisysno = t.ps0201
                           and y.busitype = '12'
                           and y.whether = 1) promiseIsUpload,
                       (select max(x.checkdate)
                          from b_kq02 x
                         where x.person_type = '1'
                           and x.user_id = t.ps0201) as checkdate,
                    fun_get_age(to_date(SUBSTR(a.idcardnumber, 7, 8), 'yyyy-MM-dd'),
                    sysdate) AS personage,
                    a.gender,
                  (
                    SELECT CASE
                    WHEN COUNT(*) > 0 THEN
                    1
                    ELSE
                    0
                    END
                    FROM b_ot01 c
                    WHERE c.busisysno = t.ps0201
                    and c.busitype = '13'
                    and c.whether = '1'
                    ) as workerFileIsUpload
                  from b_ps02 t, b_PS01 a, b_tm01 c, b_pj01 b
                 where t.ps0101 = a.ps0101
                   and t.pj0101 = b.pj0101
                   and t.tm0101 = c.tm0101
                   and t.pj0101 = #{pj0101}
                <if test="tm0101 != null and tm0101 != ''">
                    and t.TM0101 = #{tm0101}
                </if>
                <if test="cp0201 != null and cp0201 != ''">
                    and c.cp0201 = #{cp0201}
                </if>
                <if test="personName != null and personName != ''">
                    and a.NAME like '%' || #{personName} || '%'
                </if>
                <if test="teamname != null and teamname.trim() != ''">
                    and c.teamname like '%' || #{teamname} || '%'
                </if>
                <if test="address != null and address.trim() != ''">
                    and a.address like '%' || #{address} || '%'
                </if>
                <if test="name != null and name.trim() != ''">
                    and b.name like '%' || #{name} || '%'
                </if>
                <if test="inOrOut != null and inOrOut != ''">
                    and t.IN_OR_OUT = #{inOrOut}
                </if>
                 order by t.entrytime desc, t.rowid) h
         where 1 = 1
            <if test="nokqdays != null and nokqdays.trim() != ''">
                and h.checkdate &lt; sysdate - #{nokqdays}
            </if>
    </select>
    <update id="updateInOrOutByIds">
        update B_PS02 t
        set t.IN_OR_OUT = #{type},
        <if test="type == 1">
            t.entrytime  = sysdate,
            t.exittime = null
        </if>
        <if test="type == 2">
            t.exittime  = sysdate
        </if>
             where t.PS0201 in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </update>

    <select id="selectPersonByIds" resultType="io.renren.modules.supdevicetask.dto.PersonDTO">
        select a.NAME, b.PS0201 userId, b.ISSUECARDPICURL imageUrl
        from b_ps01 a,
             b_ps02 b where b.PS0101 = a.PS0101
                        and b.PS0201 in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </select>
    <select id="selectCountPs0101" resultType="java.lang.Integer">
        select count(0)
        from (select t.ps0101, t.pj0101
              from b_ps02 t
              union all
              select p.ps0101, a.pj0101
              from b_ps04 a, b_ps03 p where a.ps0301 = p.ps0301) c
        where c.pj0101 = #{pj0101}
          and c.ps0101 = #{ps0101}
    </select>
    <select id="exportContract" resultType="io.renren.modules.enterprise.ps02.dto.Ps02ContractDTO">
        select t.ps0201,
               e.name                                projectname,
               a.birthday,
               d.corpname,
               d.legalman,
               d.address                             corpaddress,
               a.name,
               decode(a.gender, 1, '男', 2, '女')      gender,
               a.idcardnumber,
               a.address,
               a.cellphone,
               d.corpcode,
               (select g.dict_label
                from sys_dict_type f,
                     sys_dict_data g
                where f.dict_type = 'WORKTYPECODE'
                  and f.id = g.dict_type_id
                  and g.dict_value = t.worktypecode) worktypecode,
               d.linkcellphone
        from b_ps02 t,
             b_ps01 a,
             b_tm01 b,
             b_cp02 c,
             b_cp01 d,
             b_pj01 e
        where t.tm0101 = b.tm0101
          and t.ps0101 = a.ps0101
          and t.pj0101 = e.pj0101
          and b.cp0201 = c.cp0201
          and c.cp0101 = d.cp0101
          and t.ps0201 = #{ps0201}
    </select>
    <select id="getEmpRecordListData" resultType="io.renren.modules.enterprise.ps02.dto.Ps02EmpRecordDTO">
        select (select b.name from b_pj01 b where b.pj0101 = t.pj0101)     projectName,
               a.name,
               a.idcardnumber                                              idCardNumber,
               t.worktypecode                                              workTypeCode,
               (select c.teamname from b_tm01 c where c.tm0101 = t.tm0101) teamName,
               t.entrytime                                                 entryTime,
               t.exittime                                                  exitTime,
               t.in_or_out                                                 inOrOut
        from b_ps02 t,
             b_ps01 a
        where t.ps0101 = a.ps0101
          and t.ps0101 = #{ps0101}
    </select>
    <select id="getUserInfoByPs0201" resultType="io.renren.modules.enterprise.ps01.entity.Ps01Entity">
        select b.name from b_ps01 b, b_ps02 p where b.ps0101 = p.ps0101 and p.ps0201 = #{ps0201}
    </select>
    <select id="exportRosterPage" resultType="io.renren.modules.enterprise.ps02.dto.Ps02ExportDTO">
        select rownum as sno,g.* from (select h.*
          from (select a.name,
                        case
                        when check_date(substr(a.idcardnumber, 7, 8)) = '1' then
                        FUN_GET_AGE(to_date(substr(a.idcardnumber, 7, 8),
                        'yyyyMMdd'),
                        sysdate)
                        end as age,
                       (select x.dict_label
                          from sys_dict_data x
                         where x.dict_type_id = 1160061077912860000
                           and x.dict_value = a.gender) as gender,
                       (select x.dict_label
                          from sys_dict_data x
                         where x.dict_type_id = 1
                           and x.dict_value = a.nation) as nation,
                       a.idcardnumber,
                       t.payrollbankcardnumber,
                       (select x.dict_label
                          from sys_dict_data x
                         where x.dict_type_id = 116
                           and x.dict_value = t.payrolltopbankcode) as payrolltopbankcode,
                        t.payrollbankname,t.payrollno,
                       (select x.corpname from b_cp01 x where x.cp0101=c.cp0101) as corpname,
                       b.teamname,
                       (select x.dict_label
                          from sys_dict_data x
                         where x.dict_type_id = 4
                           and x.dict_value = t.isteamleader) as isteamleader,
                       (select x.dict_label
                          from sys_dict_data x
                         where x.dict_type_id = 206
                           and x.dict_value = t.worktypecode) as worktypecode,
                       a.address,
                       a.cellphone,
                       t.entrytime,
                       t.exittime,
                       (select x.dict_label
                          from sys_dict_data x
                         where x.dict_type_id = 1274882193764470000
                           and x.dict_value = t.in_or_out) as in_or_out,
                       t.tm0101,
                       t.create_date
                  from B_PS02 t, b_ps01 a, b_tm01 b, b_cp02 c
                 where t.ps0101 = a.ps0101
                   and t.tm0101 = b.tm0101
                   and b.cp0201 = c.cp0201
                   and t.pj0101 = #{pj0101}
                union all
                select b.name,
                        case
                        when check_date(substr(b.idcardnumber, 7, 8)) = '1' then
                        FUN_GET_AGE(to_date(substr(b.idcardnumber, 7, 8),
                        'yyyyMMdd'),
                        sysdate)
                        end as age,
                       (select x.dict_label
                          from sys_dict_data x
                         where x.dict_type_id = 1160061077912860000
                           and x.dict_value = b.gender) as gender,
                       (select x.dict_label
                          from sys_dict_data x
                         where x.dict_type_id = 1
                           and x.dict_value = b.nation) as nation,
                       b.idcardnumber,
                       '',
                       '',
                       '',
                       '',
                        (select x.corpname from b_cp01 x where x.cp0101=c.cp0101) as corpname,
                        (select x.dict_label
                        from sys_dict_data x
                        where x.dict_type_id = 14
                        and x.dict_value = c.corptype) || '-管理' as teamname,
                       '否',
                       (select x.dict_label
                          from sys_dict_data x
                         where x.dict_type_id = 1390482614346858498
                           and x.dict_value = t.jobtype) as worktypecode,
                       b.address,
                       b.cellphone,
                       t.entrytime,
                       t.exittime,
                       (select x.dict_label
                          from sys_dict_data x
                         where x.dict_type_id = 1274882193764470000
                           and x.dict_value = t.in_or_out) as in_or_out,
                       -100 as tm0101,
                       t.create_date
                  from B_PS04 t, b_ps03 a, b_ps01 b, b_cp02 c
                 where t.ps0301 = a.ps0301
                   and a.ps0101 = b.ps0101
                    and t.cp0201 = c.cp0201
                   and t.pj0101 = #{pj0101}) h
                where 1 = 1
        <if test="tm0101 != null and tm0101 != ''">
            and h.tm0101 = #{tm0101}
        </if>
        <if test="startTime != null and startTime != ''">
            and to_char(h.entrytime,'yyyy-MM') &gt;= #{startTime}
            and to_char(h.entrytime,'yyyy-MM') &lt;= #{endTime}
        </if>
        <if test="address != null and address.trim() != ''">
            and h.address like '%' || #{address} || '%'
        </if>
         order by h.entrytime desc,h.create_date desc,h.tm0101) g
    </select>
    <select id="selectMemo" resultType="java.lang.String">
        select '本月新增' ||
               (select count(1)
                  from b_ps02 x
                 where x.pj0101 = #{pj0101}
                <if test="tm0101 != null and tm0101 != ''">
                    and x.tm0101 = #{tm0101}
                </if>
                   and to_char(x.entrytime, 'yyyy-MM') = to_char(sysdate, 'yyyy-MM')) || '人，本月退场' ||
               (select count(1)
                  from b_ps02 x
                 where x.pj0101 = #{pj0101}
                <if test="tm0101 != null and tm0101 != ''">
                    and x.tm0101 = #{tm0101}
                </if>
                   and x.in_or_out = '2'
                   and to_char(x.exittime, 'yyyy-MM') = '2023-05') || '人，本月在场' ||
               (select count(1)
                  from b_ps02 x
                 where x.pj0101 = #{pj0101}
                <if test="tm0101 != null and tm0101 != ''">
                    and x.tm0101 = #{tm0101}
                </if>
                   and x.in_or_out = '1') || '人' as memo
          from dual
    </select>
    <update id="updateTeamleader">
        update b_ps02 t set t.isteamleader = '0' where t.tm0101 = #{tm0101}
    </update>
    <select id="selectSeqAttInfo" resultType="java.lang.Long">
        select zjnmg.seq_att_info.nextval from dual
    </select>
    <select id="insertAttInfo">
        insert into zjnmg.att_info
              (att_id, att_name, att_type, area_code, busiid, file_path)
            values
              (#{ATT_ID},
               #{ATT_NAME},
               'jpg',
               '5115',
               #{ATT_ID},
               #{FILE_PATH})
    </select>
    <insert id="insertProjectWorkerInfo">
        insert into zjnmg.project_worker_info
              (pj0101,
               corp_code,
               corp_name,
               team_sys_no,
               team_name,
               worker_name,
               is_team_leader,
               id_card_type,
               id_card_number,
               age,
               gender,
               nation,
               address,
               head_image,
               politics_type,
               culture_level_type,
               grant_org,
               work_type,
               native_place,
               mobile,
               has_contract,
               id,
               busiid)
            values
              (#{PJ0101},
               #{CORP_CODE},
               #{CORP_NAME},
               #{TEAM_SYS_NO},
               #{TEAM_NAME},
               #{WORKER_NAME},
               #{IS_TEAM_LEADER},
               '1',
               #{ID_CARD_NUMBER},
               #{AGE},
               #{GENDER},
               #{NATION},
               #{ADDRESS},
               #{HEAD_IMAGE},
               #{POLITICS_TYPE},
               #{CULTURE_LEVEL_TYPE},
               #{GRANT_ORG},
               #{WORK_TYPE},
               #{NATIVE_PLACE},
               #{MOBILE},
               '是',
               zjnmg.seq_project_worker_info.nextval,
               #{BUSIID})
    </insert>
    <insert id="insertProjectWorkerInoutInfo">
        insert into zjnmg.project_worker_inout_info
              (pj0101, id_card_number, in_out, occur_time, id, busiid)
            values
              (#{PJ0101},
               #{ID_CARD_NUMBER},
               '1',
               #{OCCUR_TIME},
               zjnmg.seq_project_worker_inout_info.nextval,
               #{BUSIID})
    </insert>

    <update id="batchExit">
        update B_PS02 t
        set t.IN_OR_OUT = 2,
            t.exittime = sysdate
        where t.PS0201 in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </update>

    <select id="selectDevicePersonByIds" resultType="io.renren.modules.supdevicetask.dto.PersonDTO">

        select c.userId, d.name, c.userType, c.imageurl
        from (select a.ps0201 as userId, '1' as userType, a.ps0101, a.ISSUECARDPICURL as imageUrl
              from b_ps02 a
              where a.pj0101 = #{pj0101}
                and a.in_or_out = '1'
              union all
              select b.ps0401 as userId, '2' as userType, e.ps0101, b.PHOTO as imageUrl
              from b_ps04 b, b_ps03 e
              where b.ps0301 = e.ps0301
                and b.pj0101 = #{pj0101}
                and b.in_or_out = '1') c,
             b_ps01 d
        where c.ps0101 = d.ps0101
          and c.userId in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </select>

    <select id="getBatchImportContract" resultType="io.renren.modules.enterprise.ps02.dto.Ps02BatchImportContractDTO">
        select b.name, p.ps0201 as userId
        from b_ps01 b, b_ps02 p
        where b.ps0101 = p.ps0101
          and p.pj0101 = #{pj0101}
          and b.idcardnumber = #{idcardnumber}
    </select>

    <select id="getWorkerInOrOutData" resultType="io.renren.modules.enterprise.ps02.dto.Ps02InOrOutDTO">

        select b.name, p.ps0201, t.entry_or_exit_time, t.in_or_out
        from b_ps01 b, b_ps02 p, b_ps06 t
        where b.ps0101 = p.ps0101
          and p.ps0201 = t.ps0201
          and p.ps0201 = #{params.ps0201}
          <if test="params.inorout != null and params.inorout != ''">
              and p.in_or_out = #{params.inorout}
          </if>
        order by t.ps0601 desc
    </select>

    <select id="getWorkerOverageNum" resultType="java.lang.String">
        SELECT SUM(CASE
                       WHEN gender = '1' AND
                            fun_get_age(to_date(SUBSTR(b.idcardnumber, 7, 8), 'yyyy-MM-dd'),
                                        sysdate) > 60 THEN
                           1
                       WHEN gender = '2' AND
                            fun_get_age(to_date(SUBSTR(b.idcardnumber, 7, 8), 'yyyy-MM-dd'),
                                        sysdate) > 55 THEN
                           1
                       ELSE
                           0
            END) AS count
        FROM b_ps01 b, b_ps02 p
        where b.ps0101 = p.ps0101
          and p.pj0101 = #{pj0101}
          and p.in_or_out = '1'
    </select>

    <select id="getWorkerOveragePage" resultType="io.renren.modules.enterprise.ps02.dto.Ps02WorkerOverageDTO">
        SELECT t.ps0201,
               t.entrytime,
               a.gender,
               t.tm0101,
               t.pj0101,
               c.teamname,
               b.name AS projectname,
               (SELECT h.corpname
                FROM b_cp01 h
                WHERE h.cp0101 = (SELECT f.cp0101
                                  FROM b_tm01 e, b_cp02 f
                                  WHERE e.cp0201 = f.cp0201
                                    AND e.tm0101 = t.tm0101)) AS corpname,
               a.name AS personname,
               t.worktypecode ,
               a.idcardnumber,
               fun_get_age(to_date(SUBSTR(a.idcardnumber, 7, 8), 'yyyy-MM-dd'),
                           sysdate) AS personage
        FROM b_ps02 t, b_PS01 a, b_tm01 c, b_pj01 b
        WHERE t.ps0101 = a.ps0101
          AND t.pj0101 = b.pj0101
          AND t.tm0101 = c.tm0101
          AND t.in_or_out = '1'
         AND t.pj0101 = #{params.pj0101}
         <if test="params.teamname != null and params.teamname.trim() != ''">
             and c.teamname like '%' || #{params.teamname} || '%'
         </if>
          AND ((a.gender = '1' AND
                fun_get_age(to_date(SUBSTR(a.idcardnumber, 7, 8), 'yyyy-MM-dd'),
                            sysdate) > 60) OR
               (a.gender = '2' AND
                fun_get_age(to_date(SUBSTR(a.idcardnumber, 7, 8), 'yyyy-MM-dd'),
                            sysdate) > 55))
    </select>

    <select id="getByIdcardnumberAndName" resultType="io.renren.modules.enterprise.ps02.entity.Ps02Entity">
        select t.*
        from B_PS02 t inner join B_PS01 t1 on t.ps0101 = t1.ps0101
        where t1.idcardnumber = #{idcardnumber} and t1.name = #{name} and in_or_out = '1' and t.pj0101 = #{pj0101}
    </select>

    <select id="getWorkerOverageNum" resultType="java.lang.String">
        SELECT SUM(CASE
                       WHEN gender = '1' AND
                            fun_get_age(to_date(SUBSTR(b.idcardnumber, 7, 8), 'yyyy-MM-dd'),
                                        sysdate) > 60 THEN
                           1
                       WHEN gender = '2' AND
                            fun_get_age(to_date(SUBSTR(b.idcardnumber, 7, 8), 'yyyy-MM-dd'),
                                        sysdate) > 55 THEN
                           1
                       ELSE
                           0
            END) AS count
        FROM b_ps01 b, b_ps02 p
        where b.ps0101 = p.ps0101
          and p.pj0101 = #{pj0101}
          and p.in_or_out = '1'
    </select>

    <select id="countPs02ByTm0101" resultType="java.lang.Long">
        select count(1)
        from b_ps02 t1
                 inner join b_tm01 t2
                            on t1.tm0101 = t2.tm0101
        where t1.in_or_out = '1'
          and t2.tm0101 = #{tm0101}
    </select>
</mapper>