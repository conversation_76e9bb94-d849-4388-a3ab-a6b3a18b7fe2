<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pw02.dao.Pw02Dao">

    <resultMap type="io.renren.modules.enterprise.pw02.entity.Pw02Entity" id="pw02Map">
        <result property="pw0201" column="PW0201"/>
        <result property="pw0101" column="PW0101"/>
        <result property="ps0201" column="PS0201"/>
        <result property="kqts" column="KQTS"/>
        <result property="yfgz" column="YFGZ"/>
        <result property="kh" column="KH"/>
        <result property="khh" column="KHH"/>
        <result property="sfgz" column="SFGZ"/>
        <result property="xtkqts" column="XTKQTS"/>
        <result property="idcardnumber" column="IDCARDNUMBER"/>
        <result property="name" column="NAME"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="month" column="MONTH"/>
        <result property="mobile" column="MOBILE"/>
        <result property="reason" column="REASON"/>
        <result property="auditstatus" column="AUDITSTATUS"/>
        <result property="importstatus" column="IMPORTSTATUS"/>
        <result property="status" column="STATUS"/>
    </resultMap>

    <select id="getList" resultType = "io.renren.modules.enterprise.pw02.dto.Pw02DTO">
        select pw0201,
               kqts,
               (kqts - xtkqts) cy,
               yfgz,
               kh,
               khh,
               sfgz,
               xtkqts,
               idcardnumber,
               name,
               mobile,
               reason,
               importstatus,
               auditstatus
        from b_pw02
        <where>
            <if test="params.name != null and params.name != ''">
                and name like '%' || #{params.name} || '%'
            </if>
            <if test="params.pw0101 != null and params.pw0101 != ''">
                and pw0101 = #{params.pw0101}
            </if>
            <if test="params.importstatus != null and params.importstatus != ''">
                and importstatus = #{params.importstatus}
            </if>
            <if test="params.auditstatus != null and params.auditstatus != ''">
                and auditstatus = #{params.auditstatus}
            </if>
        </where>
        order by importstatus desc
    </select>

    <insert id="batchInsert">
        INSERT ALL
        <foreach collection="list" item="item" index="index">
            INTO b_pw02 (
            pw0201, pw0101, ps0201, kqts, yfgz, kh, sfgz, xtkqts,
            idcardnumber, name, pj0101, month, mobile, reason, auditstatus,
            importstatus, khh, create_date, update_date
            ) VALUES (
            #{item.pw0201}, #{item.pw0101}, #{item.ps0201}, #{item.kqts}, #{item.yfgz},
            #{item.kh}, #{item.sfgz}, #{item.xtkqts}, #{item.idcardnumber},
            #{item.name}, #{item.pj0101}, #{item.month}, #{item.mobile}, #{item.reason},
            '0', #{item.importstatus}, #{item.khh}, SYSDATE, SYSDATE
            )
        </foreach>
        SELECT * FROM DUAL
    </insert>

    <select id="listByPj0101AndMonth" resultType="io.renren.modules.enterprise.pw02.excel.Pw02ImportExcel">
        select
            t1.name name,
            t1.idcardnumber idcardnumber,
            t1.cellphone mobile,
            t.payrollbankcardnumber kh,
            t.payrolltopbankcode khh
        from B_PS02 t
                 inner join B_PS01 t1 on t1.ps0101 = t.ps0101
        where pj0101 = #{pj0101}
          and in_or_out = '1'
          and to_char(entrytime, 'yyyyMM') &lt;= #{month}
    </select>

    <select id="getExportList" resultType="io.renren.modules.enterprise.pw02.excel.Pw02ImportExcel">
        select kqts,
               kh,
               sfgz,
               yfgz,
               xtkqts,
               idcardnumber,
               name,
               mobile,
               reason,
               (select g.dict_label
                from sys_dict_type f, sys_dict_data g
                where f.dict_type = 'PAY_BANK_CODE'
                  and f.id = g.dict_type_id
                  and g.dict_value = khh) khh
        from B_PW02
        where pw0101 = #{pw0101}
    </select>

    <select id="getSfgzByPw0101" resultType="java.lang.Double">
        select sum(sfgz)
        from B_PW02
        where pw0101 = #{pw0101}
    </select>

    <update id="updateAuditStatusByPw0101">
        update B_PW02
        set auditstatus = '0'
        where pw0101 = #{pw0101}
    </update>

</mapper>