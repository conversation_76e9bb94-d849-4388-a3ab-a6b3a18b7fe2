server:
  undertow:
    # 指定工作者线程的 I/0 线程数，默认为 2 或者 CPU 的个数
    io-threads: 4
    # 指定工作者线程个数,默认为 I/O线程个数的8倍
    worker-threads: 32
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    # 每块buffer的空间大小,越小的空间被利用越充分，不要设置太大，以免影响其他应用，合适即可
    buffer-size: 1024
    # 是否分配的直接内存(NIO直接分配的堆外内存)
    direct-buffers: true
  servlet:
    context-path: /project/
    session:
      cookie:
        http-only: true
  port: 8013

spring:
  # 环境 dev|test|prod
  profiles:
    active: dev
  messages:
    encoding: UTF-8
    basename: i18n/messages
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 200MB
      enabled: true
  activiti:
    check-process-definitions: false

# 是否开启redis缓存  true开启   false关闭
renren.redis.open: true
# 附件储存方式：1、本地储存；2、附件服务器储存 ，默认附件服务器存储
upload:
  isOpenLocal: false
#mybatis
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: io.renren.modules.*.entity
  global-config:
    #数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: id_worker
    banner: false
  #原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
# 虹软配置
config:
  arcface-sdk:
    app-id: APn3yXgphD7QveLkiQvi4xSX2XqFk5dCWAjjxwxUJKDo
    sdk-key: Bw45ks1jMxqYbjH9UdgcEttR7Z3Aq9NyiVEwdbfV4Hi4
    sdkLibPath: D:\gknmgFile\ymzjarcface\WIN64
    thread-pool-size: 5
# 工资单水印名称
salaryWatermarkName: 泸州市建管二中心劳务用工监管平台
