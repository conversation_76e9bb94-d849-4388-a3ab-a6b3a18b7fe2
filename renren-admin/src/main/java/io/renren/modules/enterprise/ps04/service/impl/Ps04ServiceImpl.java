package io.renren.modules.enterprise.ps04.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.common.service.CommonService;
import io.renren.common.constants.CommonConstants;
import io.renren.common.exception.RenException;
import io.renren.common.file.ImageUtil;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.Result;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.admin.sys.dao.SysDictDataDao;
import io.renren.modules.enterprise.cp01.dao.Cp01Dao;
import io.renren.modules.enterprise.cp01.entity.Cp01Entity;
import io.renren.modules.enterprise.cp02.dao.Cp02Dao;
import io.renren.modules.enterprise.cp02.entity.Cp02Entity;
import io.renren.modules.enterprise.pj01.dao.Pj01Dao;
import io.renren.modules.enterprise.pj01.entity.Pj01Entity;
import io.renren.modules.enterprise.ps01.dao.Ps01Dao;
import io.renren.modules.enterprise.ps01.entity.Ps01Entity;
import io.renren.modules.enterprise.ps02.dao.Ps02Dao;
import io.renren.modules.enterprise.ps02.entity.Ps02Entity;
import io.renren.modules.enterprise.ps03.dao.Ps03Dao;
import io.renren.modules.enterprise.ps03.dto.Ps03PageDTO;
import io.renren.modules.enterprise.ps03.entity.Ps03Entity;
import io.renren.modules.enterprise.ps03audit.dao.Ps03AuditDao;
import io.renren.modules.enterprise.ps03audit.entity.Ps03AuditEntity;
import io.renren.modules.enterprise.ps04.dao.Ps04Dao;
import io.renren.modules.enterprise.ps04.dto.*;
import io.renren.modules.enterprise.ps04.entity.Ps04Entity;
import io.renren.modules.enterprise.ps04.service.Ps04Service;
import io.renren.modules.enterprise.ps04.vo.Ps04SaveVO;
import io.renren.modules.enterprise.ps04audit.dao.Ps04AuditDao;
import io.renren.modules.enterprise.ps04audit.entity.Ps04AuditEntity;
import io.renren.modules.enterprise.ps13.dao.Ps13Dao;
import io.renren.modules.enterprise.ps13.entity.Ps13Entity;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.renren.modules.ot01.service.Ot01Service;
import io.renren.modules.supdevicetask.dto.PersonDTO;
import io.renren.modules.supdevicetask.service.SupDeviceTaskService;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 管理人员参建信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2022-11-19
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class Ps04ServiceImpl extends CrudServiceImpl<Ps04Dao, Ps04Entity, Ps04DTO> implements Ps04Service {
    @Autowired
    private Ps04Dao ps04Dao;
    @Autowired
    private Ps04AuditDao ps04AuditDao;
    @Autowired
    private CommonService commonService;
    @Autowired
    private Ps02Dao ps02Dao;
    @Autowired
    private Ps01Dao ps01Dao;
    @Autowired
    private Ps03Dao ps03Dao;
    @Autowired
    private Ps03AuditDao ps03AuditDao;
    @Autowired
    private Cp02Dao cp02Dao;
    @Autowired
    private Cp01Dao cp01Dao;
    @Autowired
    private SupDeviceTaskService supDeviceTaskService;
    @Autowired
    private Ot01Service ot01Service;
    @Autowired
    private SysDictDataDao dictDataDao;
    @Autowired
    private Pj01Dao pj01Dao;
    @Autowired
    private Ps13Dao ps13Dao;
    @Resource
    private SqlSessionFactory sqlSessionFactory;

    /**
     * 总包单位
     */
    private static final String GENERAL_CONTRACT = "9";
    /**
     * 监理单位
     */
    private static final String SUPERVISION_UNIT = "7";
    /**
     * 总包单位项目经理
     */
    private static final String PROJECT_MANAGER = "1009";
    /**
     * 总监理工程师
     */
    private static final String CHIEF_SUPERINTENDENT = "1001";
    /**
     * 人员类型
     */
    private final static String PERSON_TYPE = "2";


    private final static String IMAGE = "data:image";

    @Override
    public QueryWrapper<Ps04Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<Ps04Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    public PageData<Ps04PageDTO> ps04Page(Map<String, Object> params) {
        paramsToLike(params, "name");
        paramsToLike(params, "cpname");
        params.put("deptId", SecurityUser.getDeptId());
        IPage<Ps04Entity> page = getPage(params, "", false);
        List<Ps04PageDTO> list = baseDao.getListData(params);
        return getPageData(list, page.getTotal(), Ps04PageDTO.class);
    }

    @Override
    public PageData<DispatchRecordDTO> dispatchRecords(Map<String, Object> params) {
        //params.put("deptId", SecurityUser.getDeptId());
        params.put("cp0101", commonService.getUserCp0101());
        IPage<Ps04Entity> page = getPage(params, "", false);
        List<Ps03PageDTO> list = baseDao.dispatchRecords(params);
        return getPageData(list, page.getTotal(), DispatchRecordDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result savePs04(Ps04SaveVO dto) {
        Result<Object> result = new Result<>();
        Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
        dto.setWorkertype("2");
        //处理头像问题
        if (StringUtils.isNotBlank(dto.getPhoto())) {
            boolean file = StrUtil.startWith(dto.getPhoto(), IMAGE);
            if (file) {
                String imagePath = ImageUtil.base64ToImage(dto.getPhoto());
                dto.setPhoto(imagePath);
                dto.setHeadimageurl(imagePath);
            }
        } else {
            return result.error("人员头像不能为空！");
        }
        //查询人员基本信息
        Ps01Entity ps01 = ps01Dao.selectOne(new QueryWrapper<Ps01Entity>().eq("IDCARDNUMBER", dto.getIdcardnumber()));
        if (ps01 == null) {
            //保存人员基本信息
            ps01 = new Ps01Entity();
            //判断证件号码是否正确
            boolean validCard = IdcardUtil.isValidCard(dto.getIdcardnumber());
            if (!validCard) {
                return result.error("证件号码不正确，请检查后重试！");
            }
            //获取人员出生日期
            dto.setBirthday(IdcardUtil.getBirthDate(dto.getIdcardnumber()));
            //处理读卡器民族问题
            if (!StringUtils.isNumeric(dto.getNation())) {
                String nation = dictDataDao.getNationByLabel(dto.getNation());
                dto.setNation(nation);
            }
            //处理性别
            int genderByIdCard = IdcardUtil.getGenderByIdCard(dto.getIdcardnumber());
            dto.setGender(genderByIdCard == 1 ? "1" : "2");
            BeanUtil.copyProperties(dto, ps01, CopyOptions.create().setIgnoreNullValue(true));
            ps01Dao.insert(ps01);
        }
        //校验人员在选择入场的企业在职信息
        Cp02Entity cp02 = cp02Dao.selectById(dto.getCp0201());
        //校验人员入场问题（本项目管理人员、工人，其他项目管理人员）
        checkPersonEnter(ps01.getPs0101(), pj0101, cp02.getCp0101());
        Ps03Entity ps03 = ps03Dao.selectOne(new QueryWrapper<Ps03Entity>().eq("CP0101", cp02.getCp0101()).eq("PS0101", ps01.getPs0101()));
        if (ps03 == null) {
            ps03 = new Ps03Entity();
            ps03.setPj0101(pj0101);
            ps03.setCp0101(cp02.getCp0101());
            ps03.setPs0101(ps01.getPs0101());
            ps03.setManagestatus("1");
            ps03.setPhoto(dto.getPhoto());
            ps03Dao.insert(ps03);
        } else {
            ps03.setPj0101(pj0101);
            ps03Dao.updateById(ps03);
        }
        Ps04Entity ps04 = new Ps04Entity();
        BeanUtil.copyProperties(dto, ps04, CopyOptions.create().setIgnoreNullValue(true));
        ps04.setPj0101(pj0101);
        ps04.setPs0301(ps03.getPs0301());
        ps04.setInOrOut("1");
        baseDao.insert(ps04);
        //校验关键岗位
        Integer count = ps13Dao.selectCount(new QueryWrapper<Ps13Entity>()
                .eq("jobtype", dto.getJobtype())
                .eq("corptype", cp02.getCorptype()));
        if (count > 0) {
            if (dto.getCertificateFiles().size() > 0) {
                ot01Service.doFileRelation(dto.getCertificateFiles(), ps04.getPs0401());
            } else {
                return result.error("关键岗位证书文件不能为空！");
            }
        }

        // 处理附件问题
        if (CollectionUtil.isNotEmpty(dto.getManagerCertificateFiles())) {
            ot01Service.doFileRelation(dto.getManagerCertificateFiles(), ps04.getPs0401());
        }

        // 添加考勤设备下发待处理数据
        List<PersonDTO> personDTOList = this.getPersonIntoDevices(ps04.getPs0401(), ps01.getName(), ps04.getPhoto());
        supDeviceTaskService.personIntoDevicesByPj0101(personDTOList, CommonConstants.PERSON_INTO, CommonConstants.MANAGER_TYPE, ps04.getPj0101());
        return result;
    }

    @Override
    public Result updateInfo(Ps04DTO dto) {
        Result<Object> result = new Result<>();
        boolean file = false;
        //如果修改头像，则重新下发设备
        if (StringUtils.isNotBlank(dto.getPhoto())) {
            file = StrUtil.startWith(dto.getPhoto(), IMAGE);
            if (file) {
                String imagePath = ImageUtil.base64ToImage(dto.getPhoto());
                dto.setPhoto(imagePath);
                dto.setHeadimageurl(imagePath);
            }
        } else {
            return result.error("人员头像不能为空！");
        }
        Ps03Entity ps03 = ps03Dao.selectById(dto.getPs0301());
        BeanUtil.copyProperties(dto, ps03, CopyOptions.create().setIgnoreNullValue(true));
        ps03Dao.updateById(ps03);
        Ps01Entity ps01 = ps01Dao.selectById(ps03.getPs0101());
        int genderByIdCard = IdcardUtil.getGenderByIdCard(dto.getIdcardnumber());
        dto.setGender(genderByIdCard == 1 ? "1" : "2");
        BeanUtil.copyProperties(dto, ps01, CopyOptions.create().setIgnoreNullValue(true));
        ps01Dao.updateById(ps01);
        Ps04Entity ps04 = baseDao.selectById(dto.getPs0401());
        Cp02Entity cp02 = cp02Dao.selectById(ps04.getCp0201());
        Long cp0201 = ps04.getCp0201();
        String jobtype = ps04.getJobtype();
        BeanUtil.copyProperties(dto, ps04, CopyOptions.create().setIgnoreNullValue(true));
        Integer count = ps13Dao.selectCount(new QueryWrapper<Ps13Entity>()
                .eq("jobtype", dto.getJobtype())
                .eq("corptype", cp02.getCorptype()));
        if (count > 0) {
            // 管理人员修改关键岗位类型后，资质确认状态改为待审核
            if (!jobtype.equals(dto.getJobtype())) {
                List<Ot01DTO> list = ot01Service.loadBusinessData(ps04.getPs0401(), "50");
                Long[] ids = list.stream().mapToLong(Ot01DTO::getOt0101).boxed().toArray(Long[]::new);
                ot01Service.deleteFiles(ids);
                ps04.setIsconfirm("0");
                ps04.setConfirmresult("");
                ps04.setConfirmor("");
                ps04.setConfirmDate(null);
                ps04Dao.updateById(ps04);
            }
            if (dto.getCertificateFiles().size() > 0) {
                // 新增证书，修改管理人员资质审核状态
                List<Ot01DTO> ot01List = ot01Service.loadBusinessData(ps04.getPs0401(), "50");
                if ((CollectionUtil.isNotEmpty(ot01List) && dto.getCertificateFiles().size() != ot01List.size())) {
                    ps04.setIsconfirm("0");
                    ps04.setConfirmresult("");
                    ps04.setConfirmor("");
                    ps04.setConfirmDate(null);
                    ps04Dao.updateById(ps04);
                }
                ot01Service.doFileRelation(dto.getCertificateFiles(), ps04.getPs0401());
            } else {
                return result.error("关键岗位证书文件不能为空！");
            }
        }
        // 查询最新的资质审核状态
        Ps04Entity ps04Entity = ps04Dao.getPs04IsConfirm(ps04.getPs0401());
        ps04.setIsconfirm(ps04Entity.getIsconfirm());
        ps04.setConfirmDate(ps04Entity.getConfirmDate());
        ps04.setConfirmresult(ps04Entity.getConfirmresult());
        ps04.setConfirmor(ps04Entity.getConfirmor());
        ps04Dao.updateById(ps04);

        if (file) {
            List<PersonDTO> personDTOList = this.getPersonIntoDevices(ps04.getPs0401(), ps01.getName(), ps04.getPhoto());
            supDeviceTaskService.personIntoDevicesByPj0101(personDTOList, CommonConstants.PERSON_INTO, CommonConstants.MANAGER_TYPE, ps04.getPj0101());
        }
        String stupstatus = CommonUtils.userProjectInfo().getStupstatus();
        if (!"0".equals(stupstatus)) {
            if ("1".equals(ps04.getInOrOut())) {
                if (!cp0201.equals(dto.getCp0201()) || !jobtype.equals(dto.getJobtype())) {
                    throw new RenException("已上报省厅，不允许修改所属单位或岗位！请退场后修改重新入场！");
                }
            }
        }

        // 处理附件问题
        if (CollectionUtil.isNotEmpty(dto.getManagerCertificateFiles())) {
            ot01Service.updateUnlessFiles("54", ps04.getPs0401());
            ot01Service.doFileRelation(dto.getManagerCertificateFiles(), ps04.getPs0401());
        }
        return result;
    }

    /**
     * 处理ps01
     *
     * @param ps01Entity
     */
    private Long dealPs01(Ps01Entity ps01Entity) {
        // 如果当前身份证已存在则修改信息，不存在则添加
        Ps01Entity ps01 = ps01Dao.selectOne(new QueryWrapper<Ps01Entity>()
                .eq("IDCARDNUMBER", ps01Entity.getIdcardnumber()));
        if (ObjectUtil.isNull(ps01)) {
            ps01Dao.insert(ps01Entity);
            return ps01Entity.getPs0101();
        } else {
            BeanUtil.copyProperties(ps01Entity, ps01, CopyOptions.create().setIgnoreNullValue(true));
            ps01Dao.updateById(ps01);
            return ps01.getPs0101();
        }
    }

    /**
     * 操作ps03数据
     */
    private void dealManagerInfo(Ps04SaveVO dto, Ps01Entity ps01Entity, Long cp0101, Long ps0101) {
        Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
        Ps03Entity ps03Entity = ps03Dao.selectOne(new QueryWrapper<Ps03Entity>()
                .eq("PJ0101", pj0101)
                .eq("CP0101", cp0101)
                .eq("PS0101", ps0101)
        );
        if (ObjectUtil.isNull(ps03Entity)) {
            Ps03AuditEntity ps03AuditEntity = new Ps03AuditEntity();
            Integer ps03AuditNum = ps03AuditDao.selectCount(new QueryWrapper<Ps03AuditEntity>()
                    .eq("PJ0101", pj0101)
                    .eq("CP0101", cp0101)
                    .eq("PS0101", ps0101)
                    .and(
                            query -> query.eq("AUDITSTATUS", CommonConstants.PS04_CONFIRM_WAIT)
                                    .or()
                                    .eq("AUDITSTATUS", CommonConstants.PS04_CONFIRM_SUCCESS)
                    )
            );
            if (ps03AuditNum > 0) {
                throw new RenException("当前管理人员在职信息在此项目已提交审核或已审核成功,请勿重复提交");
            } else {
                ps03AuditEntity.setPs0101(ps0101);
                ps03AuditEntity.setPj0101(pj0101);
                ps03AuditEntity.setCp0101(cp0101);
                // 1在职
                ps03AuditEntity.setManagestatus("1");
//                ps03AuditEntity.setHasbuyinsurance(dto.getHasbuyinsurance());
                ps03AuditEntity.setPhoto(dto.getPhoto());
                // 入职时间 暂默认给当前时间
                ps03AuditEntity.setInductiontime(new Date());
                ps03AuditDao.insert(ps03AuditEntity);

                // 处理ps04
                delPs04Info(dto, pj0101, ps03AuditEntity.getPs0301());
            }
        } else {
            // 处理ps04
            delPs04Info(dto, pj0101, ps03Entity.getPs0301());
        }

    }


    /**
     * 处理ps04数据
     *
     * @param dto
     * @param pj0101
     * @param ps0301
     */
    private void delPs04Info(Ps04SaveVO dto, Long pj0101, Long ps0301) {
        Ps04Entity ps04Entity = ps04Dao.selectOne(new QueryWrapper<Ps04Entity>()
                .eq("PJ0101", pj0101)
                .eq("CP0201", dto.getCp0201())
                .eq("PS0301", ps0301)
        );
        if (ObjectUtil.isNull(ps04Entity)) {

            Integer ps04Num = ps04AuditDao.selectCount(new QueryWrapper<Ps04AuditEntity>()
                    .eq("PJ0101", pj0101)
                    .eq("CP0201", dto.getCp0201())
                    .eq("PS0301", ps0301)
                    .and(
                            query -> query.eq("AUDITSTATUS", CommonConstants.PS04_CONFIRM_WAIT)
                                    .or()
                                    .eq("AUDITSTATUS", CommonConstants.PS04_CONFIRM_SUCCESS)
                    )
            );
            if (ps04Num > 0) {
                throw new RenException("当前管理人员在此项目的信息已提交审核或已审核成功,请勿重复提交");
            } else {
                Ps04AuditEntity ps04AuditEntity = new Ps04AuditEntity();
                ps04AuditEntity.setPj0101(pj0101);
                ps04AuditEntity.setCp0201(dto.getCp0201());
                // 进场时间默认为当前
                ps04AuditEntity.setEntrytime(new Date());
                ps04AuditEntity.setJobtype(dto.getJobtype());
                ps04AuditEntity.setPs0301(ps0301);
                ps04AuditEntity.setPhoto(dto.getPhoto());
                ps04AuditEntity.setInOrOut(CommonConstants.WORK_IN);

                ps04AuditDao.insert(ps04AuditEntity);
            }
        }
    }


    /**
     * 校验项目经理和总监理工程师的唯一性
     */
    private void checkPartManager(Long cp0201, String jobType) {
        //查询参建单位类型
        String partType = ps04Dao.selectTypeById(cp0201);
        //查询数量
        Integer count = ps04Dao.selectByCount(partType, jobType, CommonUtils.userProjectInfo().getPj0101());
        //总包单位项目经理
        if (GENERAL_CONTRACT.equals(partType) && PROJECT_MANAGER.equals(jobType)) {
            if (count > 0) {
                throw new RenException("项目经理已存在,请勿重复录入!");
            }
        }
        if (SUPERVISION_UNIT.equals(partType) && CHIEF_SUPERINTENDENT.equals(jobType)) {
            if (count > 0) {
                throw new RenException("总监理工程师已存在,请勿重复录入!");
            }
        }
    }

    @Override
    public Ps04DTO getInfo(Long id) {
        Ps04Entity ps04 = baseDao.selectById(id);
        Ps03Entity ps03 = ps03Dao.selectById(ps04.getPs0301());
        //查询人员基本信息
        Ps01Entity ps01 = ps01Dao.selectById(ps03.getPs0101());
        ps01.setHeadimageurl(ps04.getPhoto());
        Ps04DTO dto = new Ps04DTO();
        BeanUtil.copyProperties(ps01, dto, CopyOptions.create().setIgnoreNullValue(true));
        BeanUtil.copyProperties(ps04, dto, CopyOptions.create().setIgnoreNullValue(true));
        //证书附件
        dto.setCertificateFiles(ot01Service.loadBusinessData(id, "50"));

        if (ObjectUtil.isNotNull(dto)) {
            List<Ot01DTO> ot01DTOS = ot01Service.loadBusinessData(dto.getPs0401(), "54");
            dto.setManagerCertificateFiles(ot01DTOS);
        }
        return dto;
    }

    @Override
    public void exit(Long[] ids) {
        List<Long> longs = Arrays.asList(ids);
        baseDao.updateInOrOutByIds(longs, "2");
        baseDao.exitPs03ByIds(longs);

        List<PersonDTO> personDTOList = baseDao.selectPersonByIds(longs);

        supDeviceTaskService.personIntoDevices(personDTOList, CommonConstants.PERSON_DELETE, CommonConstants.MANAGER_TYPE);
    }

    @Override
    public void entry(Long[] ids) {
        List<Long> longs = Arrays.asList(ids);
        baseDao.updateInOrOutByIds(longs, "1");
        if (ids.length > 0) {
            Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
            Pj01Entity pj01 = pj01Dao.selectById(pj0101);
            if (!"0".equals(pj01.getStupstatus())) {
                for (Long id : ids) {
                    Ps04Entity ps04 = baseDao.selectById(id);
                    Cp02Entity cp02 = cp02Dao.selectById(ps04.getCp0201());
                    Cp01Entity cp01 = cp01Dao.selectById(cp02.getCp0101());
                    Long ps0101 = ps03Dao.selectById(ps04.getPs0301()).getPs0101();
                    Ps01Entity ps01 = ps01Dao.selectById(ps0101);
                    //写入头像上报表
                    Map<String, Object> headImageParams = new HashMap<>();
                    Long att_id = ps04Dao.selectSeqAttInfo();
                    headImageParams.put("ATT_ID", att_id);
                    headImageParams.put("ATT_NAME", ps01.getPs0101() + ps04.getPs0401());
                    headImageParams.put("FILE_PATH", ps04.getPhoto());
                    ps04Dao.insertAttInfo(headImageParams);
                    //写入管理人员上报表
                    Map<String, Object> pmParams = new HashMap<>();
                    pmParams.put("PJ0101", pj01.getPj0101());
                    pmParams.put("CORP_NAME", cp01.getCorpname());
                    pmParams.put("CORP_CODE", cp01.getCorpcode());
                    pmParams.put("CORP_TYPE", cp02.getCorptype());
                    pmParams.put("P_TYPE", ps04.getJobtype());
                    pmParams.put("PM_NAME", ps01.getName());
                    pmParams.put("PM_ID_CARD_NUMBER", ps01.getIdcardnumber());
                    pmParams.put("PM_PHONE", ps01.getCellphone());
                    pmParams.put("GRANT_ORG", ps01.getGrantorg());
                    pmParams.put("NATION", ps01.getNation());
                    pmParams.put("POLITICS_TYPE", ps01.getPoliticstype());
                    pmParams.put("CULTURE_LEVEL_TYPE", ps01.getCultureleveltype());
                    pmParams.put("ADDRESS", ps01.getAddress());
                    pmParams.put("HEAD_IMAGE", att_id);
                    pmParams.put("BUSIID", ps04.getPs0401());
                    ps04Dao.insertProjectPmInfo(pmParams);
                    //写入人员进场数据上报表
                    Map<String, Object> inoutParams = new HashMap<>();
                    inoutParams.put("PJ0101", pj01.getPj0101());
                    inoutParams.put("ID_CARD_NUMBER", ps01.getIdcardnumber());
                    inoutParams.put("OCCUR_TIME", new Date());
                    ps04Dao.insertProjectWorkerInoutInfo(inoutParams);
                }
            }
        }
        // 下发设备
        List<PersonDTO> personDTOList = ps04Dao.selectPersonByIds(longs);
        supDeviceTaskService.personIntoDevices(personDTOList, CommonConstants.PERSON_INTO, CommonConstants.MANAGER_TYPE);
    }

    private void checkPersonEnter(Long ps0101, Long pj0101, Long cp0101) {
        //判断该人员是否在本项目工人列表中
        Ps02Entity ps02 = ps02Dao.selectOne(new QueryWrapper<Ps02Entity>().eq("PS0101", ps0101).eq("PJ0101", pj0101).eq("IN_OR_OUT", "1"));
        if (ps02 != null) {
            throw new RenException("该人员已在本项目工人队伍入场，请将工人退场后重试！");
        }
        //判断该人员是否在本项目管理人员列表中
        Long isps04 = ps04Dao.isInProject(ps0101, pj0101);
        if (isps04 > 0) {
            throw new RenException("该人员已在本项目管理人员队伍入场，请勿重复录入！");
        }
        //判断该人员是否在其他项目管理人员列表中
        Long isotherps04 = ps04Dao.isInOtherProject(ps0101, pj0101, cp0101);
        if (isotherps04 > 0) {
            throw new RenException("该人员已在其他项目,其他企业管理人员队伍中入场，请检查人员所属企业或联系项目退场后重试！");
        }
    }

    @Override
    public void deviceAddPerson(Long[] ids) {
        List<Long> longs = Arrays.asList(ids);
        List<PersonDTO> person = baseDao.selectPersonByIds(longs);
        supDeviceTaskService.personIntoDevices(person, CommonConstants.PERSON_INTO, CommonConstants.MANAGER_TYPE);
    }

    private List<PersonDTO> getPersonIntoDevices(Long userId, String name, String imageUrl) {
        ArrayList<PersonDTO> personDTOList = new ArrayList<>();
        PersonDTO personDTO = new PersonDTO();
        personDTO.setUserId(userId);
        personDTO.setName(name);
        personDTO.setImageUrl(imageUrl);
        personDTOList.add(personDTO);
        return personDTOList;
    }

    @Override
    public Result info(Map<String, Object> params) {
        Result<Object> result = new Result<>();
        Long ps0401 = Long.valueOf(params.get("ps0401").toString());
        //查询人员基础信息
        Ps04PageDTO info = baseDao.selectInfoById(ps0401);
        return result.ok(info);
    }

    @Override
    public Result attendanceTJ(Map<String, Object> params) {
        Result<Object> result = new Result<>();
        String startdate = params.get("startdate").toString();
        String enddate = params.get("enddate").toString();
        DateTime start = DateUtil.parse(startdate, "yyyy-MM");
        DateTime end = DateUtil.parse(enddate, "yyyy-MM");
        Date projectstart = CommonUtils.userProjectInfo().getStartdate();
        if (start.before(projectstart)) {
            throw new RenException("选择日期请勿早于项目开工日期！");
        }
        if (DateUtil.between(start, end, DateUnit.WEEK) > 53) {
            throw new RenException("选择时间请勿超过1年！");
        }
        //查询人员基考勤统计
        Attendance44StatisticsDTO dto = baseDao.selectAttendanceTJ(params);
        List<AttendanceMonthDTO> kqlist = baseDao.getAttendanceMonthList(params);
        dto.setKqlist(kqlist);
        return result.ok(dto);
    }

    @Override
    public void updateIsConfirm(Long ps0401) {

        Ps04Entity ps04 = ps04Dao.selectById(ps0401);
        Cp02Entity cp02 = cp02Dao.selectById(ps04.getCp0201());
        Integer count = ps13Dao.selectCount(new QueryWrapper<Ps13Entity>()
                .eq("jobtype", ps04.getJobtype())
                .eq("corptype", cp02.getCorptype()));
        if (count > 0) {
            ps04.setConfirmresult("");
            ps04.setConfirmor("");
            ps04.setConfirmDate(null);
            ps04.setIsconfirm("0");

            ps04Dao.updateById(ps04);
        }
    }
}