package io.renren.modules.enterprise.ps04.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.ps03.dto.Ps03PageDTO;
import io.renren.modules.enterprise.ps04.dto.Attendance44StatisticsDTO;
import io.renren.modules.enterprise.ps04.dto.AttendanceMonthDTO;
import io.renren.modules.enterprise.ps04.dto.Ps04DTO;
import io.renren.modules.enterprise.ps04.dto.Ps04PageDTO;
import io.renren.modules.enterprise.ps04.entity.Ps04Entity;
import io.renren.modules.supdevicetask.dto.PersonDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 管理人员参建信息表
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-11-19
 */
@Mapper
public interface Ps04Dao extends BaseDao<Ps04Entity> {


    /**
     * 列表分页查询
     *
     * @param params
     * @return
     */
    List<Ps04PageDTO> getListData(Map<String, Object> params);


    List<Ps03PageDTO> dispatchRecords(Map<String, Object> params);

    /**
     * 查询参建单位类型
     * @param cp0201 cp0201
     * @return String
     */
    String selectTypeById(Long cp0201);


    /**
     * 查询关键岗位的数量
     *
     * @param partType 参建单位类型
     * @param jobType  岗位类型
     * @param pj0101   项目ID
     * @return Integer
     */
    Integer selectByCount(@Param("partType") String partType, @Param("jobType") String jobType, @Param("pj0101") Long pj0101);

    Long isInProject(Long ps0101, Long pj0101);

    Long isInOtherProject(Long ps0101, Long pj0101,Long cp0101);

    void updateInOrOutByIds(@Param("list") List<Long> ids, @Param("type") String type);

    void exitPs03ByIds(@Param("list") List<Long> ids);

    List<PersonDTO> selectPersonByIds(@Param("list") List<Long> ids);

    Long selectSeqAttInfo();


    void insertAttInfo(Map<String, Object> headImageParams);

    void insertProjectPmInfo(Map<String, Object> pmParams);

    void insertProjectWorkerInoutInfo(Map<String, Object> inoutParams);

    Long getManagerSupplement(@Param("pj0101") Long pj0101);

    Ps04PageDTO selectInfoById(Long ps0401);

    Attendance44StatisticsDTO selectAttendanceTJ(Map<String, Object> params);

    List<AttendanceMonthDTO> getAttendanceMonthList(Map<String, Object> params);

    Long getKeyJobCount(@Param("jobtype") String jobtype,@Param("corptype") String corptype,@Param("pj0101") Long pj0101);

    Ps04Entity getPs04IsConfirm(Long ps0401);
}