package io.renren.modules.enterprise.ps04.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 管理人员参建信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2022-11-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_PS04")
public class Ps04Entity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long ps0401;
    /**
     * 项目ID
     */
    private Long pj0101;
    /**
     * 参建单位ID
     */
    private Long cp0201;
    /**
     * 人员ID
     */
    private Long ps0301;
    /**
     * 岗位类型
     */
    private String jobtype;
    /**
     * 项目采集照片
     */
    private String photo;
    /**
     * 进场时间
     */
    private Date entrytime;
    /**
     * 退场时间
     */
    private Date exittime;
    /**
     * 进退场状态
     */
    private String inOrOut;
    /**
     * 资质确认状态
     */
    private String isconfirm;
    /**
     * 资质确认结果
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String confirmresult;
    /**
     * 资质确认人
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String confirmor;
    /**
     * 资质确认时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date confirmDate;
    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}