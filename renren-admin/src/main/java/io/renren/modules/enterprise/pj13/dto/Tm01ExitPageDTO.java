package io.renren.modules.enterprise.pj13.dto;

import io.renren.common.annotation.desensitized.Desensitized;
import io.renren.common.enums.DesenTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "工人退场分页")
public class Tm01ExitPageDTO {
    @ApiModelProperty(value = "班组名称")
    private String teamname;
    @ApiModelProperty(value = "班组id")
    private Long tm0101;
    @ApiModelProperty(value = "负责人姓名")
    private String responsiblepersonname;
    @ApiModelProperty(value = "负责人联系电话")
    @Desensitized(type = DesenTypeEum.MOBILE_PHONE)
    private String responsiblepersonphone;
    @ApiModelProperty(value = "负责人身份证")
    @Desensitized(type = DesenTypeEum.ID_CARD)
    private String responsiblepersonidnumber;
    @ApiModelProperty(value = "入场时间")
    private Date entrytime;
    @ApiModelProperty(value = "审核状态(0待审核，1通过，2不通过)")
    private String auditstatus;
    @ApiModelProperty(value = "审核失败原因")
    private String auditreason;
}
