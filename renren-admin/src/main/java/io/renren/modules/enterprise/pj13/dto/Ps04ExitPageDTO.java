package io.renren.modules.enterprise.pj13.dto;

import io.renren.common.annotation.desensitized.Desensitized;
import io.renren.common.enums.DesenTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "管理人员退场分页")
public class Ps04ExitPageDTO {
    @ApiModelProperty(value = "管理人员姓名")
    private String name;
    @ApiModelProperty(value = "管理人员id")
    private Long ps0401;
    @ApiModelProperty(value = "身份证号")
    @Desensitized(type = DesenTypeEum.ID_CARD)
    private String idcardnumber;
    @ApiModelProperty(value = "性别")
    private String gender;
    @ApiModelProperty(value = "手机号码")
    @Desensitized(type = DesenTypeEum.MOBILE_PHONE)
    private String cellphone;
    @ApiModelProperty(value = "参建单位名称")
    private String corpName;
    @ApiModelProperty(value = "岗位类型")
    private String jobType;
    @ApiModelProperty(value = "入场时间")
    private Date entrytime;
    @ApiModelProperty(value = "审核状态(0待审核，1通过，2不通过)")
    private String auditstatus;
    @ApiModelProperty(value = "审核失败原因")
    private String auditreason;
}
